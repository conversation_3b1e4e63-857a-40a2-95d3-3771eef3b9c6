package org.fh.timer;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.fh.config.WxCpTpServiceMyImpl;
import org.fh.entity.common.AttendanceEntity;
import org.fh.entity.common.EmployeeEntity;
import org.fh.mapper.dsno1.common.AttendanceMapper;
import org.fh.mapper.dsno1.common.OvertimeApplyMapper;
import org.fh.service.common.AcAttendanceService;
import org.fh.service.common.AttendanceAnalysisService;
import org.fh.service.common.AttendanceService;
import org.fh.service.common.DailyAttendanceAnalysisService;
import org.fh.service.common.EmployeeService;
import org.fh.service.common.MachineService;
import org.fh.service.common.OutsourcingPersonService;
import org.fh.service.common.OvertimeApplyService;
import org.fh.service.common.ReportHcService;
import org.fh.service.common.ReportOvertimeService;
import org.fh.service.common.ScheduleService;
import org.fh.service.common.UserCountReportService;
import org.fh.service.common.WithDrawnService;
import org.fh.service.wx.WxCpMessageService;
import org.fh.util.DateUtil;
import org.fh.util.ObjectUtils;
import org.fh.util.RedisUtil;
import org.fh.util.ResultHelper;
import org.fh.util.StatusConst;
import org.fh.util.UserInfo;
import org.fh.util.WxUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TimerTaskService {

  @Resource
  private RedisUtil redisUtil;

  @Resource
  private OvertimeApplyService overtimeApplyService;

  @Resource
  private OvertimeApplyMapper overtimeApplyMapper;

  @Resource
  private AttendanceMapper attendanceMapper;

  @Resource
  private RuntimeService runtimeService;

  @Resource
  private WxCpMessageService wxCpMessageService;

  @Resource
  private UserCountReportService userCountReportService;

  @Resource
  private AttendanceService attendanceService;

  @Resource
  private MachineService machineService;

  @Resource
  private WithDrawnService withDrawnService;

  @Resource
  private EmployeeService employeeService;

  @Resource
  private AttendanceAnalysisService analysisService;

  @Resource
  private ReportHcService reportHcService;

  @Resource
  private ReportOvertimeService reportOvertimeService;

  @Resource
  private ScheduleService scheduleService;

  @Resource
  private WxCpTpServiceMyImpl wxCpTpService;

  @Resource
  private DailyAttendanceAnalysisService dailyAttendanceAnalysisService;

  @Resource
  private OutsourcingPersonService outsourcingPersonService;

  @Resource
  private AcAttendanceService acAttendanceService;

  private final WxUtil wxUtil;

  @Autowired
  public TimerTaskService(WxUtil wxUtil) {
    this.wxUtil = wxUtil;
  }

  //文件夹目录
  //@Value("${kayang.fileupload.path}")
  //private String uploadPath;


  /**
   * @Description 同步员工到微信企业号(正)-每小时同步一次
   * <AUTHOR>
   * @Date 2019/11/28 9:32
   * @Return
   * @Param
   * @Throws Exception
   */
  @Scheduled(cron = "0 0 * * * ?")
  public void synchUserToWehcat() {
    //从缓存中取
    String regex = "\"errcode\":(\\d+),";
    Pattern pattern = Pattern.compile(regex);
    try {
      Set<String> dataSet = redisUtil.getKeyCollection("wechat");
      List<Long> departmentList = new ArrayList<>();
      departmentList.add(17L);
      if (!ObjectUtils.isNull(dataSet)) {
        for (String o : dataSet) {
          EmployeeEntity employeeEntity = (EmployeeEntity) redisUtil.getDataObject(o);
          System.out.println(
              "工号：" + employeeEntity.getEmpId() + "   姓名：" + employeeEntity.getName());

          UserInfo user = new UserInfo();
          user.setName(employeeEntity.getName());
          user.setUserid(employeeEntity.getEmpId());
          user.setDepartment(departmentList);
          user.setEmail(UUID.randomUUID() + "@rinsys.com");

          try {
            wxUtil.addUser("ww15c509c183b7e8ee", user);

            redisUtil.deleteKeyObject(o);
          } catch (Exception e) {
            System.out.println("异常信息：" + e.getMessage());
          }
        }
      }
    } catch (Exception e) {
      System.out.println("出现异常：" + e.getMessage());
    }
  }

  /**
   * @Description 加班超时自动撤回-每分钟执行一次
   * <AUTHOR>
   * @Date 2019/11/28 10:39
   * @Return
   * @Param
   * @Throws Exception
   */
//  @Scheduled(cron = "0 */1 * * * ? ")
  /*public void updateOvertimeType() {
    try {
      List<String> processInstanceIds = overtimeApplyMapper.queryTimeoutOvertime(
          DateUtil.getTime());
      String msg = "超时自动取消加班";
      if (!ObjectUtils.isNull(processInstanceIds)) {
        for (int i = 0; i < processInstanceIds.size(); i++) {
          try {
            runtimeService.deleteProcessInstance(processInstanceIds.get(i), msg);
          } catch (Exception e) {

          }
        }
        overtimeApplyMapper.updateOvertimeStatus(processInstanceIds);
      }

    } catch (Exception e) {
      e.printStackTrace();
    }
  }*/


  /**
   * @Description 加班超时自动提交-每分钟执行一次
   * <AUTHOR>
   * @Date 2019/11/28 10:39
   * @Return
   * @Param
   * @Throws Exception
   */
  @Scheduled(cron = "0 */1 * * * ? ")
  public void autoCommitOvertime() {
    try {
      overtimeApplyService.autoApproveOvertimeApply();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * @Description 查询所有需要发送消息的领班
   * <AUTHOR>
   * @Date 2019/12/2 9:51
   * @Return
   * @Param
   * @Throws Exception
   */
  @Scheduled(cron = "0 0 9/12 * * ? ")
  public void queryCautionLeader() {
    try {
      String date = DateUtil.getOffDayDate("-5");
      List<String> empIds = attendanceMapper.queryCaution(date);
      if (!ObjectUtils.isNull(empIds)) {
        for (int i = 0; i < empIds.size(); i++) {
          String content = wxUtil.getMsgContentByType(StatusConst.MessageType.REMINDER,
              new HashMap<>(), "", empIds.get(i));
          wxUtil.sendMessage(empIds.get(i), content);
        }
      }
    } catch (Exception e) {

    }
  }

  /**
   * @Description (上午)定时发送未标记考勤记录至目标邮箱
   * <AUTHOR>
   * @Date 2020/6/28 12:52
   * @Return
   * @Param
   * @Throws Exception
   */
  //@Scheduled(cron = "0 30 10 * * ? ")//每天上午十点三十执行
  public void forenoonSendAttendanceToEmail() {
    try {
      attendanceService.sendAttendanceToEmail();
    } catch (Exception e) {
      System.out.println("上午考勤记录同步异常信息" + e.getMessage());
      attendanceService.sendEmailToManage("考勤记录发送邮件或者更改状态异常了");
    }
  }

  /**
   * @Description (晚上)定时发送未标记考勤记录至目标邮箱
   * <AUTHOR>
   * @Date 2020/6/28 12:52
   * @Return
   * @Param
   * @Throws Exception
   */
  //@Scheduled(cron = "0 0 23 * * ? ")//每天晚上十一点执行
  public void afternoonSendAttendanceToEmail() {
    try {
      attendanceService.sendAttendanceToEmail();
    } catch (Exception e) {
      System.out.println("晚上考勤记录同步异常信息" + e.getMessage());
      attendanceService.sendEmailToManage("考勤记录发送邮件或者更改状态异常了");
    }
  }


  /**
   * @Description 每天上午十一点执行查看考勤记录
   * <AUTHOR>
   * @Date 2020/4/9 18:05
   * @Return
   * @Param
   * @Throws Exception
   */
  //@Scheduled(cron = "0 0 11 * * ? ")//每天上午十一点执行
  public void sendAttendanceUpEmail() {
    try {
      String date = DateUtil.getDay();//获取当天
      String startTime = "";
      String endTime = date + " 10:30:00";
      startTime = DateUtil.getOffDayDate("-1");
      startTime = startTime + " 23:30:00";
      attendanceService.sendEmailToEmployee(startTime, endTime);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * @Description 每天0点执行查看考勤记录
   * <AUTHOR>
   * @Date 2020/4/9 15:19
   * @Return
   * @Param
   * @Throws Exception
   */
  //@Scheduled(cron = "0 0 0 * * ? ")
  public void sendAttendanceToEmail() {
    try {
      String startTime = "";
      String endTime = "";
      startTime = DateUtil.getOffDayDate("-1");
      startTime = startTime + " 10:30:00";
      endTime = DateUtil.getOffDayDate("-1");
      endTime = endTime + " 23:30:00";
      attendanceService.sendEmailToEmployee(startTime, endTime);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * @Description 从缓存中获取考勤记录(正)
   * <AUTHOR>
   * @Date 2020/1/25 11:10
   * @Return
   * @Param
   * @Throws Exceptionz
   */
  @Scheduled(cron = "*/5 * * * * ?")
  public void addAttendanceHistory() {
    try {
      Set<String> keySet = redisUtil.getKeyListData("attendance");
      if (!ObjectUtils.isNull(keySet)) {
        for (String str : keySet) {
          System.out.println("人脸缓存ID：" + str);
          AttendanceEntity attendanceEntity = (AttendanceEntity) redisUtil.getDataObject(str);
          if (ObjectUtils.isNullStr(attendanceEntity.getAttendantDate())) {
            log.debug("打卡日期为空");
            continue;
          }
          if (attendanceEntity.getAttendantDate().length() < 18) {
            log.debug("打卡日期格式不正确");
            continue;
          }
          String attendantDate = attendanceEntity.getAttendantDate().substring(0, 10);
          String attendantTime = attendanceEntity.getAttendantDate()
              .substring(11, attendanceEntity.getAttendantDate().length());
          attendanceEntity.setAttendantDate(attendantDate);
          attendanceEntity.setAttendantTime(attendantTime);
          attendanceEntity.setId(str);
          ResultHelper result = attendanceService.addAttendance(attendanceEntity);
          if (result.getStatus() == 0) {
            redisUtil.deleteKeyObject(str);
          } else {
            System.out.println("工号：" + attendanceEntity.getEmpId() + "日期："
                + attendanceEntity.getAttendantDate() + "考勤记录添加失败，失败原因："
                + result.getMessage());
            //log.debug("打卡日期为空");
          }
        }
      }
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * @Description 实时监控考勤机(正)
   * <AUTHOR>
   * @Date 2020/5/29 9:54
   * @Return
   * @Param
   * @Throws Exception
   */
  @Scheduled(cron = "0 */1 * * * ?")
  public void monitoringMachine() {
    try {
      machineService.monitoringMachine();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * @Description 处理异常数据
   * <AUTHOR>
   * @Date 2020/4/28 18:54
   * @Return
   * @Param
   * @Throws Exception
   */
  @Scheduled(cron = "*/1 * * * * ?")
  public void addErrorAttendanceHistory() {
    try {
      Set<String> keySet = redisUtil.getKeyListData("errorAttendance_");
      if (!ObjectUtils.isNull(keySet)) {
        for (String str : keySet) {
          AttendanceEntity attendanceEntity = (AttendanceEntity) redisUtil.getDataObject(str);
          if (ObjectUtils.isNullStr(attendanceEntity.getAttendantDate())) {
            log.debug("打卡日期为空");
            attendanceEntity.setAttendantClock("打卡日期为空");
            redisUtil.setData(
                "conductAttendance_" + UUID.randomUUID().toString().replaceAll("-", ""),
                attendanceEntity);
            redisUtil.deleteKeyObject(str);
            continue;
          }
          if (attendanceEntity.getAttendantDate().length() < 19) {
            log.debug("打卡日期格式不正确");
            attendanceEntity.setAttendantClock("打卡日期格式不正确");
            redisUtil.setData(
                "conductAttendance_" + UUID.randomUUID().toString().replaceAll("-", ""),
                attendanceEntity);
            redisUtil.deleteKeyObject(str);
            continue;
          }
          String attendantDate = attendanceEntity.getAttendantDate().substring(0, 10);
          String attendantTime = attendanceEntity.getAttendantDate()
              .substring(11, attendanceEntity.getAttendantDate().length());
          attendanceEntity.setAttendantDate(attendantDate);
          attendanceEntity.setAttendantTime(attendantTime);
          attendanceEntity.setId(str);
          ResultHelper result = attendanceService.addAttendance(attendanceEntity);
          if (result.getStatus() == 0) {
            redisUtil.deleteKeyObject(str);
          } else {
            System.out.println("工号：" + attendanceEntity.getEmpId() + "日期："
                + attendanceEntity.getAttendantDate() + "考勤记录添加失败，失败原因："
                + result.getMessage());
            //attendanceEntity.setAttendantClock(result.getMessage());
            //redisUtil.setData("conductAttendance_"+UUID.randomUUID().toString().replaceAll("-",""),attendanceEntity);
            //log.debug("打卡日期为空");
          }
        }
      }
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * @Description 同步员工到报告数据表(每日执行一次 / / 匹配财务月最后一天)(正)
   * <AUTHOR>
   * @Date 2019/12/3 15:57
   * @Return
   * @Param
   * @Throws Exception
   */
  @Scheduled(cron = "0 0 0 * * ?")
  public void synchUserCount() {
    try {
      userCountReportService.synchUserCountToHistory();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * @Description 同步员工离职移除考勤机信息
   * <AUTHOR>
   * @Date 2020/6/2 10:59
   * @Return
   * @Param
   * @Throws Exception
   */
  //@Scheduled(cron = "0 0 0 * * ? ")
  public void deleteMachineUser() {
    try {
      withDrawnService.deleteMachineUser();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }


  @Scheduled(cron = "0 0 0 13 * ?")//每月13号上午00:00自动排班
  public void initPlanSchedule() {
    try {
      scheduleService.initPlanSchedule("Vivian");
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Scheduled(cron = "0 0 8 * * ? ")//每天上午8点执行员工休息提醒
  public void queryBWorkTypeStart() {
    try {
      scheduleService.employeeSchedulingRestReminderToSupervisor();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * 当手机端修改员工的排班，需要发送手机微信提醒领班的上级，每天8点发送一次提醒，按班组提醒
   */
  @Scheduled(cron = "0 0 8 * * ? ")
  public void sendMessageToSupervisorAboutModifiedEmployeeSchedules() {
    try {
      scheduleService.sendMessageToSupervisorAboutModifiedEmployeeSchedules();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * 每天1:00和13:00，发送邮件告知同步结果
   */
  @Scheduled(cron = "0 0 1,13 * * *")
  public void syncDataFromKayangToFras() {
    try {
      employeeService.sendKayangSyncResultMsg(1);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Scheduled(cron = "0 50 11 * * ?")
  public void syncDataFromFrasToKayang1150() {
    try {
      employeeService.sendKayangSyncResultMsg(2);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Scheduled(cron = "0 50 23 * * ?")
  public void syncDataFromFrasToKayang2350() {
    try {
      employeeService.sendKayangSyncResultMsg(2);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * 每天下午1点，发送邮件告知考勤异常
   */
  @Scheduled(cron = "0 0 13 * * ? ")
  public void sendMsgToAbnormalAttendances() {
    try {
      dailyAttendanceAnalysisService.sendMsgToAbnormalAttendances();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }


}
