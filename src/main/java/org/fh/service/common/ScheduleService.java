package org.fh.service.common;

import org.fh.dto.common.schedule.KayangScheduleQueryDto;
import org.fh.dto.common.schedule.ScheduleQueryDto;
import org.fh.dto.common.schedule.ScheduleUpdateDto;
import org.fh.dto.common.schedule.ScheduleWorkTeamUpdateDto;
import org.fh.util.ResultHelper;
import org.fh.vo.common.EmpShiftInfoVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface ScheduleService {

    /**
     * @Description 初始化2个财务月排班
     * <AUTHOR>
     * @Date 2019/12/1 17:03
     * @Return ResultHelper
     * @Param String empId 员工工号
     * @Throws Exception
     */
    ResultHelper initPlanSchedule(String empId) throws Exception;

    ResultHelper initPlanScheduleForNextMonth(String empId) throws Exception;

    /**
     * @Description 分页查询排班管理
     * <AUTHOR>
     * @Date 2019/12/1 21:00
     * @Return ResultHelper
     * @Param ScheduleQueryDto scheduleQueryDto
     * @Throws Exception
     */
    ResultHelper queryPlanSchedulePage(ScheduleQueryDto scheduleQueryDto) throws Exception;

    /**
     * @Description 分页查询班组人员排班
     * <AUTHOR>
     * @Date 2020/4/28 10:09
     * @Return
     * @Param
     * @Throws Exception
     */
    ResultHelper queryEmployeeSchedulePage(ScheduleQueryDto scheduleQueryDto) throws Exception;

    /**
     * @Description H5按日期查询排班
     * <AUTHOR>
     * @Date 2019/12/1 23:10
     * @Return ResultHelper
     * @Param ScheduleQueryDto scheduleQueryDto
     * @Throws Exception
     */
    ResultHelper queryPlanScheduleByDate(ScheduleQueryDto scheduleQueryDto) throws Exception;

    /**
     * @Description 根据日期进行调整排班
     * <AUTHOR>
     * @Date 2019/12/2 11:00
     * @Return ResultHelper
     * @Param ScheduleUpdateDto scheduleUpdateDto
     * @Throws Exception
     */
    ResultHelper updateWorkTeamSchedule(ScheduleUpdateDto scheduleUpdateDto) throws Exception;

    /**
     * @Description 根据员工号修改排班调整
     * <AUTHOR>
     * @Date 2019/12/2 15:07
     * @Return ResultHelper
     * @Param ScheduleUpdateDto scheduleUpdateDto
     * @Throws Exception
     */
    ResultHelper updateUserSchedule(ScheduleUpdateDto scheduleUpdateDto) throws Exception;

    /**
     * @Description 修改排班班次
     * <AUTHOR>
     * @Date 2019/12/2 19:04
     * @Return ResultHelper
     * @Param ScheduleWorkTeamUpdateDto scheduleUpdateDto
     * @Throws Exception
     */
    ResultHelper updateInternetWorkTeamSchedule(ScheduleWorkTeamUpdateDto scheduleUpdateDto) throws Exception;

    /**
     * @Description 查询考勤日历排班情况
     * <AUTHOR>
     * @Date 2019/12/5 22:27
     * @Return ResultHelper
     * @Param ScheduleQueryDto scheduleQueryDto
     * @Throws Exception
     */
    ResultHelper queryScheduleByEmployee(ScheduleQueryDto scheduleQueryDto) throws Exception;

    List<EmpShiftInfoVo> queryEmployeeScheduleByDuration(ScheduleQueryDto scheduleQueryDto) throws Exception;

    /**
     * @Description 下载排班数据
     * <AUTHOR>
     * @Date 2019/12/11 16:26
     * @Return
     * @Param
     * @Throws Exception
     */
    Map<String, Object> downloadSchedule(ScheduleQueryDto scheduleQueryDto) throws Exception;

    /**
     * @Description 下载员工排班情况
     * <AUTHOR>
     * @Date 2020/4/28 14:05
     * @Return
     * @Param
     * @Throws Exception
     */
    Map<String, Object> downloadEmployeeSchedule(ScheduleQueryDto scheduleQueryDto) throws Exception;

    /**
     * @Description 下载员工排班情况
     * <AUTHOR>
     * @Date 2020/4/28 14:05
     * @Return
     * @Param
     * @Throws Exception
     */
    Map<String, Object> downloadEmployeeScheduleToKayang(
        KayangScheduleQueryDto kayangScheduleQueryDto) throws Exception;

    /**
     * @Description 上传排班数据
     * <AUTHOR>
     * @Date 2019/12/12 15:51
     * @Return ResultHelper
     * @Param
     * @Throws Exception
     */
    ResultHelper importSchedule(List<Object> dataList, String username) throws Exception;

    /**
     * @Description 导入人员排班情况
     * <AUTHOR>
     * @Date 2020/4/28 15:39
     * @Return
     * @Param
     * @Throws Exception
     */
    ResultHelper importEmployeeSchedule(List<Object> dataList, String username) throws Exception;

    /**
     * @Description 根据日期查询班组和人员排班
     * <AUTHOR>
     * @Date 2020/4/23 15:28
     * @Return
     * @Param
     * @Throws Exception
     */
    List<Map<String, String>> queryScheduleByDate(String scheduleDate) throws Exception;

    /**
     * 员工排班休息提醒领班
     *
     * @throws Exception
     */
    void employeeSchedulingRestReminderToSupervisor() throws Exception;


    void sendMessageToSupervisorAboutModifiedEmployeeSchedules() throws Exception;

    ResultHelper listEmployeeWhichChangeSchedulesBySupervisor(String leaderId,String changeDate) throws Exception;

}
