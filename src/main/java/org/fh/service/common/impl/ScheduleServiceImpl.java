package org.fh.service.common.impl;

import com.alibaba.excel.metadata.Table;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.fh.dto.common.schedule.KayangScheduleQueryDto;
import org.fh.dto.common.schedule.ScheduleChangeDetailDto;
import org.fh.dto.common.schedule.ScheduleChangeDto;
import org.fh.dto.common.schedule.ScheduleQueryDto;
import org.fh.dto.common.schedule.ScheduleRestDto;
import org.fh.dto.common.schedule.ScheduleUpdateDto;
import org.fh.dto.common.schedule.ScheduleWorkTeamUpdateDto;
import org.fh.entity.PageData;
import org.fh.entity.common.ScheduleEntity;
import org.fh.entity.common.ScheduleWorkTeamEntity;
import org.fh.entity.common.WorkGroupEntity;
import org.fh.mapper.dsno1.common.EmployeeMapper;
import org.fh.mapper.dsno1.common.ScheduleMapper;
import org.fh.mapper.dsno1.common.ScheduleRuleMapper;
import org.fh.mapper.dsno1.common.ScheduleWorkTeamMapper;
import org.fh.mapper.dsno1.common.WorkGroupMapper;
import org.fh.service.common.EmployeeService;
import org.fh.service.common.ScheduleService;
import org.fh.service.common.WorkCalendarService;
import org.fh.service.common.WorkTypeService;
import org.fh.service.wx.WxCpMessageService;
import org.fh.util.DateUtil;
import org.fh.util.ObjectUtils;
import org.fh.util.ResultCode;
import org.fh.util.ResultHelper;
import org.fh.util.StatusConst.MessageType;
import org.fh.util.Tools;
import org.fh.util.WxUtil;
import org.fh.vo.common.EmpShiftInfoVo;
import org.fh.vo.common.EmployeeVo;
import org.fh.vo.common.ScheduleRuleVo;
import org.fh.vo.common.ScheduleWorkTeamVo;
import org.fh.vo.common.WorkGroupVo;
import org.fh.vo.common.WorkTypeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ScheduleServiceImpl implements ScheduleService {

  @Resource
  private ScheduleMapper scheduleMapper;

  @Resource
  private ScheduleRuleMapper scheduleRuleMapper;

  @Resource
  private ScheduleWorkTeamMapper scheduleWorkTeamMapper;

  @Resource
  private EmployeeMapper employeeMapper;

  @Resource
  private WorkGroupMapper workGroupMapper;

  @Resource
  private WorkTypeService workTypeService;

  @Resource
  private WorkCalendarService workCalendarService;

  @Resource
  private WxCpMessageService wxCpMessageService;

  private final WxUtil wxUtil;

  @Autowired
  public ScheduleServiceImpl(WxUtil wxUtil) {
    this.wxUtil = wxUtil;
  }

  @Resource
  private EmployeeService employeeService;

  @Value("${domain.name.url}")
  private String REQUESTURL;

  /**
   * @Description 初始化2个财务月排班
   * <AUTHOR>
   * @Date 2019/12/1 17:05
   * @Return ResultHelper
   * @Param String empId
   * @Throws Exception
   */
  @Override
  @Transactional
  public ResultHelper initPlanSchedule(String empId) throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    int year = DateUtil.getYearNumber();
    int month = DateUtil.getMonthNumber();
    List<ScheduleWorkTeamEntity> scheduleList = new ArrayList<>();
    //先查询是否已经生成了排班，生成了就不需要再次生成
    String yearMonth = "";
    yearMonth = year + "-";
    if (month < 10) {
      yearMonth = yearMonth + "0" + month;
    } else {
      yearMonth = yearMonth + month;
    }

    int count = scheduleWorkTeamMapper.queryFinancialMonthCount(
        DateUtil.getFirstFinancialDayOfCurMonth());

    if (count > 0) {
      result.setStatus(1);
      result.setCode(ResultCode.FAILURE);
      result.setMessage("2个月排班已经生成，若想重新生成，请联系管理员");
      return result;
    }

    //查询所有排班规则
    List<ScheduleRuleVo> scheduleRuleVos = scheduleRuleMapper.queryWorkTeamRuleAll(null, null);
    if (ObjectUtils.isNull(scheduleRuleVos)) {//判断排班规则是否为空，防止未维护
      result.setStatus(1);
      result.setCode(ResultCode.FAILURE);
      result.setMessage("请先维护排班规则");
      return result;
    }

    //获取所有要生成的日期（2个财务月）
    List<String> dateList = DateUtil.getWorkDayOfTwoMonths(year, month);
    String maxDate = yearMonth + "-15";
    List<ScheduleWorkTeamVo> scheduleWorkTeamVos = scheduleWorkTeamMapper.queryScheduleMaxHistory(
        maxDate);
    Map<String, ScheduleWorkTeamVo> schwMap = new HashMap<>();
    for (int i = 0; i < scheduleWorkTeamVos.size(); i++) {
      schwMap.put(scheduleWorkTeamVos.get(i).getWorkTeam(), scheduleWorkTeamVos.get(i));
    }

    // 遇到国假，排班设为休息
    List<String> nationalList = workCalendarService.queryNationalList(
        dateList.get(0), dateList.get(dateList.size() - 1));
    Set<String> nationalListSet = new HashSet<>(nationalList);

    // 判断周末
    for (String date : dateList) {
      try {
        if (DateUtil.checkDayOfWeekend(date)) {
          nationalListSet.add(date);
        }
      } catch (Exception e) {
        // 忽略日期格式错误，继续处理
      }
    }

    for (int h = 0; h < scheduleRuleVos.size(); h++) {
      ScheduleRuleVo sc = scheduleRuleVos.get(h);

      // 测试代码
      /*if (!"Plating - 吴长林 - B".equals(sc.getWorkTeam())) {
        continue;
      }*/

      if (ObjectUtils.isNull(sc.getCycleDay()) || sc.getCycleDay() == 0) {
        continue;
      }
      int cycleDay = sc.getCycleDay();//获取周期天数，后面遍历需要加上然后循环生成排班
      int cycle = sc.getCycle();//获取周期数
      if (ObjectUtils.isNull(sc.getCycle()) || sc.getCycle() == 0) {
        continue;
      }
      ScheduleWorkTeamVo scheduleWorkTeamVo = schwMap.get(sc.getWorkTeam());
      String scheduleDate = "";
      //若不为空，说明有记录，需要与数据库中的最后一个小周期排班日期进行首尾相连
      if (!ObjectUtils.isNull(scheduleWorkTeamVo)) {
        scheduleDate = scheduleWorkTeamVo.getScheduleDate();
        //已经生成的日期看后面一天的理应排班
        scheduleDate = DateUtil.getOffDayDate(scheduleDate, "1");
      } else {//否则需要重新计算排班
        scheduleDate = dateList.get(0);
      }
      String dayFormat = scheduleRuleVos.get(h).getDayFormat();
      String[] dayFormatArray = dayFormat.split(",");
      int dateCount = DateUtil.getDaysForDate(scheduleDate, scheduleRuleVos.get(h).getStartDate());
      //获取需要排班的开始日期
      if (!ObjectUtils.isNull(scheduleWorkTeamVo)) {
        scheduleDate = scheduleWorkTeamVo.getScheduleDate();
      } else {
        scheduleDate = DateUtil.getOffDayDate(scheduleDate, "-1");
      }
      cycleDay = cycleDay * cycle;//周期天数乘周期数可以得到大周期天数
      cycleDay = dateCount % cycleDay;//起始时间到初始化时间的时间差取余得到所需要的排班周期前部分
      String workType = sc.getWorkTypeOne();
      String workTypes = workType;
      int residue = 0;
      if (cycleDay != 0) {//如果没有余数，只需要按照第一周期第一天开始排即可
        //如果有余数，需要找出是第几个班次，并且这个小周期还剩多少天没排
        if (cycleDay >= sc.getCycleDay()) {//判断剩余的天数是否大于小周期天数，如果大于，可以认为不是第一个周期
          residue = cycleDay % sc.getCycleDay();//剩余的天数取余排班周天天数获取到还剩多少天没排
          residue = sc.getCycleDay() - residue;
          cycleDay = cycleDay / sc.getCycleDay();//剩余天数除以小周期天数可以获取在第几个周期//如果加1，0是第一个周期，1是第二个周期
        } else {
          residue = sc.getCycleDay() - cycleDay;
          cycleDay = 0;
        }
      } else {
        cycleDay = 0;
      }
      int history = 0;

      for (int i = 0; i < dateList.size(); ) {//遍历需要生成的日期和排班规则
        String date = dateList.get(i);
        if (ObjectUtils.validOldAndNew(date, scheduleDate)) {//遍历的日期比已经生成的日期要大
          if (residue != 0) {//说明不是从周期第一个开始，可能只有几个
            for (int k = 0; k < residue; k++) {
              if (cycleDay == 0) {
                workType = sc.getWorkTypeOne();
              } else if (cycleDay == 1) {
                workType = sc.getWorkTypeTwo();
              } else if (cycleDay == 2) {
                workType = sc.getWorkTypeThree();
              } else if (cycleDay == 3) {
                workType = sc.getWorkTypeFour();
              } else if (cycleDay == 4) {
                workType = sc.getWorkTypeFive();
              } else {
                workType = sc.getWorkTypeOne();
              }
              int index = (sc.getCycleDay() - (residue - k));//周期天数减当前索引得到对应的小周期具体天数排班
              String status = dayFormatArray[index];
              if ("1".equals(status)) {
                workTypes = "休息";
              } else {
                workTypes = workType;
              }
              ScheduleWorkTeamEntity scheduleWorkTeamEntity = new ScheduleWorkTeamEntity();
              scheduleWorkTeamEntity.setWorkType(workTypes);
              scheduleWorkTeamEntity.setCreateBy(empId);
              scheduleWorkTeamEntity.setWorkTeam(sc.getWorkTeam());
              scheduleWorkTeamEntity.setScheduleDate(dateList.get(i));
              scheduleWorkTeamEntity.setScheduleWorkTeamId(Tools.get32UUID());
              scheduleList.add(scheduleWorkTeamEntity);
              i++;
            }
            residue = 0;
            cycleDay++;
            continue;
          }
          i++;
          if (history % sc.getCycleDay() == 0) {//满了一个周期要换一次
            cycleDay++;
            if (cycleDay == 0) {
              workType = sc.getWorkTypeOne();
            } else {
              int cycles = sc.getCycle();
              if (cycleDay <= cycles) {
                if (cycleDay == 1) {//周期如果大于1，说明不知一个周期，需要把下一个周期赋值上去
                  workType = sc.getWorkTypeOne();
                } else if (cycleDay == 2) {
                  workType = sc.getWorkTypeTwo();
                } else if (cycleDay == 3) {
                  workType = sc.getWorkTypeThree();
                } else if (cycleDay == 4) {
                  workType = sc.getWorkTypeFour();
                } else if (cycleDay == 5) {
                  workType = sc.getWorkTypeFive();
                }
              } else {
                int cycleMode = (cycleDay - cycles) % cycles;
                if (cycleMode == 0) {
                  cycleMode = cycles;
                }
                if (cycleMode == 1) {
                  workType = sc.getWorkTypeOne();
                } else if (cycleMode == 2) {
                  workType = sc.getWorkTypeTwo();
                } else if (cycleMode == 3) {
                  workType = sc.getWorkTypeThree();
                } else if (cycleMode == 4) {
                  workType = sc.getWorkTypeFour();
                } else if (cycleMode == 5) {
                  workType = sc.getWorkTypeFive();
                }
              }
            }
            String status = dayFormatArray[0];
            if ("1".equals(status)) {
              workTypes = "休息";
            } else {
              workTypes = workType;
            }
          } else {
            int index = history % sc.getCycleDay();//周期天数减当前索引得到对应的小周期具体天数排班
            String status = dayFormatArray[index];
            if ("1".equals(status)) {
              workTypes = "休息";
            } else {
              workTypes = workType;
            }
          }
          ScheduleWorkTeamEntity scheduleWorkTeamEntity = new ScheduleWorkTeamEntity();
          scheduleWorkTeamEntity.setWorkType(workTypes);
          scheduleWorkTeamEntity.setCreateBy(empId);
          scheduleWorkTeamEntity.setWorkTeam(sc.getWorkTeam());
          scheduleWorkTeamEntity.setScheduleDate(date);
          scheduleWorkTeamEntity.setScheduleWorkTeamId(Tools.get32UUID());
          scheduleList.add(scheduleWorkTeamEntity);
          history++;
        } else {
          i++;
        }
      }
      cycleDay = history % sc.getCycleDay();//取余可以获取到已经加入末尾班次的数量
      cycleDay = sc.getCycleDay() - cycleDay;//用小周期数量减末尾数量得到还需要加入的数量
      if (cycleDay != 0 && cycleDay != sc.getCycleDay()) {//如果周期记录的数量不是整个小周期，需要补满
        scheduleList.addAll(
            createSchedule(scheduleList.get((scheduleList.size() - 1)), cycleDay, dayFormat,
                workType,nationalListSet));
      }
    }
    //遍历，如果是国假就设置为休息
    for (ScheduleWorkTeamEntity scheduleWorkTeamEntity : scheduleList) {
      if (nationalListSet.contains(scheduleWorkTeamEntity.getScheduleDate())) {
        scheduleWorkTeamEntity.setWorkType("休息");
      }
    }

    if (scheduleList.size() > 50) {
      int size = scheduleList.size();
      for (int i = 0; i < scheduleList.size(); i += 50) {
        if (i + 50 > size) {
          scheduleWorkTeamMapper.addScheduleWorkTeamList(scheduleList.subList(i, size));
        } else {
          scheduleWorkTeamMapper.addScheduleWorkTeamList(scheduleList.subList(i, i + 50));
        }
      }
    } else {
      if (!ObjectUtils.isNull(scheduleList)) {
        scheduleWorkTeamMapper.addScheduleWorkTeamList(scheduleList);
      }
    }
    return result;
  }

  @Override
  public ResultHelper initPlanScheduleForNextMonth(String empId) throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    int year = DateUtil.getYearNumberNext();
    int month = DateUtil.getMonthNumberNext();
    List<ScheduleWorkTeamEntity> scheduleList = new ArrayList<>();
    //先查询是否已经生成了下个月的排班，生成了就不需要再次生成
    String yearMonth = "";
    yearMonth = year + "-";
    if (month < 10) {
      yearMonth = yearMonth + "0" + month;
    } else {
      yearMonth = yearMonth + month;
    }

    int count = scheduleWorkTeamMapper.queryFinancialMonthCount(
        DateUtil.getFirstFinancialDayOfCurMonthNext());

    if (count > 0) {
      result.setStatus(1);
      result.setCode(ResultCode.FAILURE);
      result.setMessage("本月排班已经生成，若想重新生成，请联系管理员");
      return result;
    }

    //查询所有排班规则
    List<ScheduleRuleVo> scheduleRuleVos = scheduleRuleMapper.queryWorkTeamRuleAll(null, null);
    if (ObjectUtils.isNull(scheduleRuleVos)) {//判断排班规则是否为空，防止未维护
      result.setStatus(1);
      result.setCode(ResultCode.FAILURE);
      result.setMessage("请先维护排班规则");
      return result;
    }

    //获取所有要生成的日期
    List<String> dateList = DateUtil.getWorkDayOfMonth(year, month);
    String maxDate = yearMonth + "-15";
    List<ScheduleWorkTeamVo> scheduleWorkTeamVos = scheduleWorkTeamMapper.queryScheduleMaxHistory(
        maxDate);
    Map<String, ScheduleWorkTeamVo> schwMap = new HashMap<>();
    for (int i = 0; i < scheduleWorkTeamVos.size(); i++) {
      schwMap.put(scheduleWorkTeamVos.get(i).getWorkTeam(), scheduleWorkTeamVos.get(i));
    }
    // 遇到国假，排班设为休息
    List<String> nationalList = workCalendarService.queryNationalList(
        dateList.get(0), dateList.get(dateList.size() - 1));
    Set<String> nationalListSet = new HashSet<>(nationalList);
    // 判断周末
    for (String date : dateList) {
      try {
        if (DateUtil.checkDayOfWeekend(date)) {
          nationalListSet.add(date);
        }
      } catch (Exception e) {
        // 忽略日期格式错误，继续处理
      }
    }

    for (int h = 0; h < scheduleRuleVos.size(); h++) {
      ScheduleRuleVo sc = scheduleRuleVos.get(h);

      // 测试代码
      /*if (!"Plating - 吴长林 - B".equals(sc.getWorkTeam())) {
        continue;
      }*/

      if (ObjectUtils.isNull(sc.getCycleDay()) || sc.getCycleDay() == 0) {
        continue;
      }
      int cycleDay = sc.getCycleDay();//获取周期天数，后面遍历需要加上然后循环生成排班
      int cycle = sc.getCycle();//获取周期数
      if (ObjectUtils.isNull(sc.getCycle()) || sc.getCycle() == 0) {
        continue;
      }
      ScheduleWorkTeamVo scheduleWorkTeamVo = schwMap.get(sc.getWorkTeam());
      String scheduleDate = "";
      //若不为空，说明有记录，需要与数据库中的最后一个小周期排班日期进行首尾相连
      if (!ObjectUtils.isNull(scheduleWorkTeamVo)) {
        scheduleDate = scheduleWorkTeamVo.getScheduleDate();
        //已经生成的日期看后面一天的理应排班
        scheduleDate = DateUtil.getOffDayDate(scheduleDate, "1");
      } else {//否则需要重新计算排班
        scheduleDate = dateList.get(0);
      }
      String dayFormat = scheduleRuleVos.get(h).getDayFormat();
      String[] dayFormatArray = dayFormat.split(",");
      int dateCount = DateUtil.getDaysForDate(scheduleDate, scheduleRuleVos.get(h).getStartDate());
      //获取需要排班的开始日期
      if (!ObjectUtils.isNull(scheduleWorkTeamVo)) {
        scheduleDate = scheduleWorkTeamVo.getScheduleDate();
      } else {
        scheduleDate = DateUtil.getOffDayDate(scheduleDate, "-1");
      }
      cycleDay = cycleDay * cycle;//周期天数乘周期数可以得到大周期天数
      cycleDay = dateCount % cycleDay;//起始时间到初始化时间的时间差取余得到所需要的排班周期前部分
      String workType = sc.getWorkTypeOne();
      String workTypes = workType;
      int residue = 0;
      if (cycleDay != 0) {//如果没有余数，只需要按照第一周期第一天开始排即可
        //如果有余数，需要找出是第几个班次，并且这个小周期还剩多少天没排
        if (cycleDay >= sc.getCycleDay()) {//判断剩余的天数是否大于小周期天数，如果大于，可以认为不是第一个周期
          residue = cycleDay % sc.getCycleDay();//剩余的天数取余排班周天天数获取到还剩多少天没排
          residue = sc.getCycleDay() - residue;
          cycleDay = cycleDay / sc.getCycleDay();//剩余天数除以小周期天数可以获取在第几个周期//如果加1，0是第一个周期，1是第二个周期
        } else {
          residue = sc.getCycleDay() - cycleDay;
          cycleDay = 0;
        }
      } else {
        cycleDay = 0;
      }
      int history = 0;

      for (int i = 0; i < dateList.size(); ) {//遍历需要生成的日期和排班规则
        String date = dateList.get(i);
        if (ObjectUtils.validOldAndNew(date, scheduleDate)) {//遍历的日期比已经生成的日期要大
          if (residue != 0) {//说明不是从周期第一个开始，可能只有几个
            for (int k = 0; k < residue; k++) {
              if (cycleDay == 0) {
                workType = sc.getWorkTypeOne();
              } else if (cycleDay == 1) {
                workType = sc.getWorkTypeTwo();
              } else if (cycleDay == 2) {
                workType = sc.getWorkTypeThree();
              } else if (cycleDay == 3) {
                workType = sc.getWorkTypeFour();
              } else if (cycleDay == 4) {
                workType = sc.getWorkTypeFive();
              } else {
                workType = sc.getWorkTypeOne();
              }
              int index = (sc.getCycleDay() - (residue - k));//周期天数减当前索引得到对应的小周期具体天数排班
              String status = dayFormatArray[index];
              if ("1".equals(status)) {
                workTypes = "休息";
              } else {
                workTypes = workType;
              }
              ScheduleWorkTeamEntity scheduleWorkTeamEntity = new ScheduleWorkTeamEntity();
              scheduleWorkTeamEntity.setWorkType(workTypes);
              scheduleWorkTeamEntity.setCreateBy(empId);
              scheduleWorkTeamEntity.setWorkTeam(sc.getWorkTeam());
              scheduleWorkTeamEntity.setScheduleDate(dateList.get(i));
              scheduleWorkTeamEntity.setScheduleWorkTeamId(Tools.get32UUID());
              scheduleList.add(scheduleWorkTeamEntity);
              i++;
            }
            residue = 0;
            cycleDay++;
            continue;
          }
          i++;
          if (history % sc.getCycleDay() == 0) {//满了一个周期要换一次
            cycleDay++;
            if (cycleDay == 0) {
              workType = sc.getWorkTypeOne();
            } else {
              int cycles = sc.getCycle();
              if (cycleDay <= cycles) {
                if (cycleDay == 1) {//周期如果大于1，说明不知一个周期，需要把下一个周期赋值上去
                  workType = sc.getWorkTypeOne();
                } else if (cycleDay == 2) {
                  workType = sc.getWorkTypeTwo();
                } else if (cycleDay == 3) {
                  workType = sc.getWorkTypeThree();
                } else if (cycleDay == 4) {
                  workType = sc.getWorkTypeFour();
                } else if (cycleDay == 5) {
                  workType = sc.getWorkTypeFive();
                }
              } else {
                int cycleMode = (cycleDay - cycles) % cycles;
                if (cycleMode == 0) {
                  cycleMode = cycles;
                }
                if (cycleMode == 1) {
                  workType = sc.getWorkTypeOne();
                } else if (cycleMode == 2) {
                  workType = sc.getWorkTypeTwo();
                } else if (cycleMode == 3) {
                  workType = sc.getWorkTypeThree();
                } else if (cycleMode == 4) {
                  workType = sc.getWorkTypeFour();
                } else if (cycleMode == 5) {
                  workType = sc.getWorkTypeFive();
                }
              }
            }
            String status = dayFormatArray[0];
            if ("1".equals(status)) {
              workTypes = "休息";
            } else {
              workTypes = workType;
            }
          } else {
            int index = history % sc.getCycleDay();//周期天数减当前索引得到对应的小周期具体天数排班
            String status = dayFormatArray[index];
            if ("1".equals(status)) {
              workTypes = "休息";
            } else {
              workTypes = workType;
            }
          }
          ScheduleWorkTeamEntity scheduleWorkTeamEntity = new ScheduleWorkTeamEntity();
          scheduleWorkTeamEntity.setWorkType(workTypes);
          scheduleWorkTeamEntity.setCreateBy(empId);
          scheduleWorkTeamEntity.setWorkTeam(sc.getWorkTeam());
          scheduleWorkTeamEntity.setScheduleDate(date);
          scheduleWorkTeamEntity.setScheduleWorkTeamId(Tools.get32UUID());
          scheduleList.add(scheduleWorkTeamEntity);
          history++;
        } else {
          i++;
        }
      }
      cycleDay = history % sc.getCycleDay();//取余可以获取到已经加入末尾班次的数量
      cycleDay = sc.getCycleDay() - cycleDay;//用小周期数量减末尾数量得到还需要加入的数量
      if (cycleDay != 0 && cycleDay != sc.getCycleDay()) {//如果周期记录的数量不是整个小周期，需要补满
        scheduleList.addAll(
            createSchedule(scheduleList.get((scheduleList.size() - 1)), cycleDay, dayFormat,
                workType,nationalListSet));
      }
    }
    //遍历，如果是国假就设置为休息
    for (ScheduleWorkTeamEntity scheduleWorkTeamEntity : scheduleList) {
      if (nationalListSet.contains(scheduleWorkTeamEntity.getScheduleDate())) {
        scheduleWorkTeamEntity.setWorkType("休息");
      }
    }

    if (scheduleList.size() > 50) {
      int size = scheduleList.size();
      for (int i = 0; i < scheduleList.size(); i += 50) {
        if (i + 50 > size) {
          scheduleWorkTeamMapper.addScheduleWorkTeamList(scheduleList.subList(i, size));
        } else {
          scheduleWorkTeamMapper.addScheduleWorkTeamList(scheduleList.subList(i, i + 50));
        }
      }
    } else {
      if (!ObjectUtils.isNull(scheduleList)) {
        scheduleWorkTeamMapper.addScheduleWorkTeamList(scheduleList);
      }
    }
    return result;
  }

  public List<ScheduleWorkTeamEntity> createSchedule(ScheduleWorkTeamEntity entity, int number,
      String dayFormat, String workType, Set<String> holidayAndWeekendSet) throws Exception {
    List<ScheduleWorkTeamEntity> dataList = new ArrayList<>();
    String[] dayFormatArray = dayFormat.split(",");
    int length = dayFormatArray.length;
    String workTypes = workType;
    int indexs = length - number;
    for (int i = 1; i < (number + 1); i++) {
      int index = indexs + i - 1;//周期天数减当前索引得到对应的小周期具体天数排班
      String status = dayFormatArray[index];
      if ("1".equals(status)) {
        workTypes = "休息";
      } else {
        workTypes = workType;
      }

      String scheduleDate = DateUtil.getOffDayDate(entity.getScheduleDate(), "" + i);
      // 检查当前日期是否为周末或法定节假日，如果是则设置为"休息"
      if (holidayAndWeekendSet.contains(scheduleDate)) {
        workTypes = "休息";
      }

      ScheduleWorkTeamEntity scheduleWorkTeamEntity = new ScheduleWorkTeamEntity();
      scheduleWorkTeamEntity.setWorkType(workTypes);
      scheduleWorkTeamEntity.setCreateBy(entity.getCreateBy());
      scheduleWorkTeamEntity.setWorkTeam(entity.getWorkTeam());
      scheduleWorkTeamEntity.setScheduleDate(scheduleDate);
      scheduleWorkTeamEntity.setScheduleWorkTeamId(Tools.get32UUID());
      dataList.add(scheduleWorkTeamEntity);
    }
    return dataList;
  }

  /**
   * @Description 分页查询排班管理
   * <AUTHOR>
   * @Date 2019/12/1 21:09
   * @Return ResultHelper
   * @Param
   * @Throws Exception
   */
  @Override
  public ResultHelper queryPlanSchedulePage(ScheduleQueryDto scheduleQueryDto) throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
    int year = scheduleQueryDto.getYear();
    int month = scheduleQueryDto.getMonth();
    if (month == 1) {
      month = 12;
      year--;
    } else {
      month--;
    }
    List<String> dateList = DateUtil.getFinanceDate(year, month);
    String startDate = dateList.get(0);
    String endDate = dateList.get(1);
    System.out.println("查询参数打印：" + scheduleQueryDto.toString());
    BeanUtils.copyProperties(record, scheduleQueryDto);
    PageHelper.offsetPage(scheduleQueryDto.getRows(), scheduleQueryDto.getOffset());
    Page<ScheduleWorkTeamVo> voPage = scheduleWorkTeamMapper.queryPlanSchedulePage(record);
    List<String> paramDateList = DateUtil.getWorkDayOfMonth(year, month);
    if (!ObjectUtils.isNull(voPage)) {
      for (int i = 0; i < voPage.size(); i++) {
        BeanUtils.copyProperties(record, voPage.get(i));
        record.setStartDate(startDate);
        record.setEndDate(endDate);
        List<Map<String, String>> dataList = scheduleWorkTeamMapper.queryPlanScheduleDateList(
            record);
        if (!ObjectUtils.isNull(dataList)) {
          voPage.get(i).setDateList(dataList);
          Set<String> dateSet = new HashSet<>();
          for (int h = 0; h < dataList.size(); h++) {
            dateSet.add(dataList.get(h).get("schedule_date"));
          }
          for (int h = 0; h < paramDateList.size(); h++) {
            if (!dateSet.contains(paramDateList.get(h))) {
              Map<String, String> workTypeMap = new HashMap<>();
              workTypeMap.put("schedule_date", paramDateList.get(h));
              workTypeMap.put("work_type", "空");
              dataList.add(workTypeMap);
            }
          }
        } else {
          dataList = new ArrayList<>();
          for (int h = 0; h < paramDateList.size(); h++) {
            Map<String, String> workTypeMap = new HashMap<>();
            workTypeMap.put("schedule_date", paramDateList.get(h));
            workTypeMap.put("work_type", "空");
            dataList.add(workTypeMap);
          }
          voPage.get(i).setDateList(dataList);
        }
      }
    }
    Map<String, Object> resultMap = new HashMap<String, Object>();
    resultMap.put("data", voPage);
    resultMap.put("count", voPage.getTotal());
    resultMap.put("offset", scheduleQueryDto.getOffset());
    resultMap.put("row", scheduleQueryDto.getPage());
    result.setData(resultMap);
    return result;
  }

  /**
   * @Description 分页查询班组人员排班
   * <AUTHOR>
   * @Date 2020/4/28 10:10
   * @Return
   * @Param
   * @Throws Exception
   */
  @Override
  public ResultHelper queryEmployeeSchedulePage(ScheduleQueryDto scheduleQueryDto)
      throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
    int year = scheduleQueryDto.getYear();
    int month = scheduleQueryDto.getMonth();
    if (month == 1) {
      month = 12;
      year--;
    } else {
      month--;
    }
    List<String> scheduleDateList = DateUtil.getFinanceDate(year, month);
    String startDate = scheduleDateList.get(0);
    String endDate = scheduleDateList.get(1);
    BeanUtils.copyProperties(record, scheduleQueryDto);
    PageHelper.offsetPage(scheduleQueryDto.getRows(), scheduleQueryDto.getOffset());
    Page<EmployeeVo> employeeVos = employeeMapper.queryEmployeePageByWorkTeamAndEmp(
        scheduleQueryDto.getWorkTeam(),scheduleQueryDto.getEmpId(),scheduleQueryDto.getName());
    Page<ScheduleWorkTeamVo> voPage = new Page<>();
    Map<String, Object> resultMap = new HashMap<String, Object>();
    resultMap.put("offset", scheduleQueryDto.getOffset());
    resultMap.put("row", scheduleQueryDto.getPage());
    if (!ObjectUtils.isNull(employeeVos)) {
      //BeanUtils.copyProperties(voPage, employeeVos);
      for (int i = 0; i < employeeVos.size(); i++) {
        ScheduleWorkTeamVo vo = new ScheduleWorkTeamVo();
        vo.setEmployee(employeeVos.get(i).getEmpId());
        vo.setPlant(scheduleQueryDto.getPlant());
        vo.setWorkshop(scheduleQueryDto.getWorkshop());
        vo.setWorkTeam(scheduleQueryDto.getWorkTeam());
        vo.setName(employeeVos.get(i).getName());
        voPage.add(vo);

      }
      record.setStartDate(startDate);
      record.setEndDate(endDate);
      //班组排班
      List<Map<String, String>> dataList = scheduleWorkTeamMapper.queryPlanScheduleDateList(record);
      Map<String, String> workTypeMap = transitionWorkType(dataList);
      List<String> dateList = DateUtil.getWorkDayOfMonth(year, month);
      for (int i = 0; i < voPage.size(); i++) {
        //查询每个人员的排班情况
        Set<String> dateSet = new LinkedHashSet<>(dateList);//日期集合
        record.setEmpId(voPage.get(i).getEmployee());
        //人员排班
        record.setWorkTeam(""); //修复查询和人员排班导出不一致的问题
        Map<String, Map<String, String>> dataMap1 = scheduleMapper.queryEmployeePlanScheduleList(
            record);
        if (ObjectUtils.isNull(dataMap1)) {
          List<Map<String, String>> dataList2 = new ArrayList<>();
          for (int h = 0; h < dateList.size(); h++) {
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("schedule_date", dateList.get(h));
            if (workTypeMap.containsKey(dateList.get(h) + voPage.get(i).getWorkTeam())) {
              paramMap.put("work_type",
                  workTypeMap.get(dateList.get(h) + voPage.get(i).getWorkTeam()));
            } else {
              paramMap.put("work_type", "空");
            }
            dataList2.add(paramMap);
          }
          voPage.get(i).setDateList(dataList2);
        } else {
          List<Map<String, String>> dataList2 = new ArrayList<>();
          for (int h = 0; h < dateList.size(); h++) {
            Map<String, String> dateMap = new HashMap<>();
            String scheduleDate = dateList.get(h);
            dateMap.put("schedule_date", scheduleDate);
            if (dataMap1.containsKey(scheduleDate)) {
              Map<String, String> scheduleMap = dataMap1.get(scheduleDate);
              dateMap.put("work_type", scheduleMap.get("work_type"));
            } else {
              dateMap.put("work_type", workTypeMap.get(scheduleDate + voPage.get(i).getWorkTeam()));
            }
            dataList2.add(dateMap);
          }
          voPage.get(i).setDateList(dataList2);
        }
      }
      resultMap.put("data", voPage);
      resultMap.put("count", employeeVos.getTotal());
    }
    result.setData(resultMap);
    return result;
  }

  public static Map<String, String> transitionWorkType(List<Map<String, String>> dataList) {
    Map<String, String> workTypeMap = new HashMap<>();
    if (!ObjectUtils.isNull(dataList)) {
      for (int i = 0; i < dataList.size(); i++) {
        String scheduleDate = dataList.get(i).get("schedule_date");
        String work_team = dataList.get(i).get("work_team");
        String workType = dataList.get(i).get("work_type");
        workTypeMap.put(scheduleDate + work_team, workType);
      }
    }
    return workTypeMap;
  }

  /**
   * @Description H5按日期查询排班
   * <AUTHOR>
   * @Date 2019/12/1 23:13
   * @Return ResultHelper
   * @Param
   * @Throws Exception
   */
  @Override
  public ResultHelper queryPlanScheduleByDate(ScheduleQueryDto scheduleQueryDto) throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
    EmployeeVo employeeVo = employeeMapper.queryEmployeeByEmpId(scheduleQueryDto.getEmpId());
    //record.setWorkshop(employeeVo.getWorkshop());
    //record.setPlant(employeeVo.getPlant());
    record.setWorkTeam(employeeVo.getWorkTeam());
    record.setScheduleDate(scheduleQueryDto.getScheduleDate());
    record.setEmpId(scheduleQueryDto.getEmpId());
        /*record.setStartDate(startDate);
        record.setEndDate(endDate);*/
    List<Map<String, String>> dateList = scheduleWorkTeamMapper.queryPlanScheduleDateList(record);
    record.setWorkTeam(""); // 适配班组变更，手机端后太后显示不一致的问题
    List<Map<String, String>> userDateList = scheduleMapper.queryScheduleDateList(record);
    if (!ObjectUtils.isNull(userDateList)) {
      for (int i = 0; i < dateList.size(); i++) {
        String date = dateList.get(i).get("schedule_date");
        for (int h = 0; h < userDateList.size(); h++) {
          String userDate = userDateList.get(h).get("schedule_date");
          if (userDate.equals(date)) {
            dateList.get(i).put("work_type", userDateList.get(h).get("work_type"));
            break;
          }
        }
      }
    }
    Map<String, Object> resultMap = new HashMap<>();
    resultMap.put("data", dateList);
    result.setData(resultMap);
    return result;
  }


  /**
   * @Description 查询考勤日历排班情况
   * <AUTHOR>
   * @Date 2019/12/5 22:27
   * @Return ResultHelper
   * @Param ScheduleQueryDto scheduleQueryDto
   * @Throws Exception
   */
  @Override
  public ResultHelper queryScheduleByEmployee(ScheduleQueryDto scheduleQueryDto) throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    EmployeeVo employeeVo = employeeMapper.queryEmployeeByEmpId(scheduleQueryDto.getEmpId());
    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
        /*record.setPlant(employeeVo.getPlant());
        record.setWorkshop(employeeVo.getWorkshop());*/
    record.setWorkTeam(employeeVo.getWorkTeam());
    record.setScheduleDate(scheduleQueryDto.getScheduleDate());
    List<Map<String, String>> dataList = scheduleWorkTeamMapper.queryScheduleByEmployee(record);
    if (!ObjectUtils.isNull(dataList)) {
      record.setEmpId(scheduleQueryDto.getEmpId());
      record.setWorkTeam(""); // 适配班组变更，手机端后太后显示不一致的问题
      List<Map<String, String>> userDateList = scheduleMapper.queryScheduleDateList(record);
      if (!ObjectUtils.isNull(userDateList)) {
        for (int i = 0; i < userDateList.size(); i++) {
          String scheduleDate = userDateList.get(i).get("schedule_date");
          for (int h = 0; h < dataList.size(); h++) {
            if (scheduleDate.equals(dataList.get(h).get("schedule_date"))) {
              dataList.get(h).put("workType", userDateList.get(i).get("work_type"));
              break;
            }
          }
        }
      }
    }
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put("data", dataList);
    result.setData(dataMap);
    return result;
  }

  @Override
  public List<EmpShiftInfoVo> queryEmployeeScheduleByDuration(ScheduleQueryDto scheduleQueryDto)
      throws Exception {
    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
    EmployeeVo employeeVo = employeeMapper.queryEmployeeByEmpId(scheduleQueryDto.getEmpId());
    record.setWorkTeam(employeeVo.getWorkTeam());
    record.setEmpId(employeeVo.getEmpId());
    record.setStartDate(scheduleQueryDto.getStartDate());
    record.setEndDate(scheduleQueryDto.getEndDate());
    //查看班组排班
    List<Map<String, String>> dateList = scheduleWorkTeamMapper.queryPlanScheduleDateList(record);
    record.setWorkTeam("");
    // 查看员工排班，如果有记录则覆盖班组排班
    List<Map<String, String>> userDateList = scheduleMapper.queryScheduleDateList(record);
    if (!ObjectUtils.isNull(userDateList)) {
      for (int i = 0; i < dateList.size(); i++) {
        String date = dateList.get(i).get("schedule_date");
        for (int h = 0; h < userDateList.size(); h++) {
          String userDate = userDateList.get(h).get("schedule_date");
          if (userDate.equals(date)) {
            dateList.get(i).put("work_type", userDateList.get(h).get("work_type"));
            break;
          }
        }
      }
    }
    List<WorkTypeVo> workTypeVos = workTypeService.listAllWorkTypes();
    List<EmpShiftInfoVo> empShiftInfoVoList = new ArrayList<>();

    for (Map<String, String> item : dateList) {
      WorkTypeVo workTypeVo = workTypeVos.stream()
          .filter(workType -> workType.getName().equals(item.get("work_type"))).findFirst()
          .orElse(null);

      EmpShiftInfoVo empShiftInfoVo = new EmpShiftInfoVo();
      empShiftInfoVo.setEmpId(employeeVo.getEmpId());
      empShiftInfoVo.setScheduleDate(item.get("schedule_date"));
      // 判断是否是工作日
      if ("休息".equals(item.get("work_type"))) {
        empShiftInfoVo.setWorkday(false);
      } else {
        empShiftInfoVo.setWorkday(true);
      }
      empShiftInfoVo.setWorkType(item.get("work_type"));
      empShiftInfoVo.setStartTime(convertString2LocalTime(workTypeVo.getStartTime()));
      empShiftInfoVo.setEndTime(convertString2LocalTime(workTypeVo.getEndTime()));
      empShiftInfoVo.setMealStartTime(convertString2LocalTime(workTypeVo.getRestStartTime()));
      empShiftInfoVo.setMealEndTime(convertString2LocalTime(workTypeVo.getRestEndTime()));

      empShiftInfoVoList.add(empShiftInfoVo);
    }
    return empShiftInfoVoList;
  }

  /**
   * @Description web修改班组排班情况
   * <AUTHOR>
   * @Date 2019/12/2 19:07
   * @Return ResultHelper
   * @Param ScheduleWorkTeamUpdateDto scheduleUpdateDto
   * @Throws Exception
   */
  @Override
  public ResultHelper updateInternetWorkTeamSchedule(ScheduleWorkTeamUpdateDto scheduleUpdateDto)
      throws Exception {
    ResultHelper result = ResultHelper.builder().build();

    // 排班校验：
    // 1) 工作日不能排休息，除非是法定节假日
    // 2) 休息日只能排休息
    String scheduleDateForTeam = scheduleUpdateDto.getScheduleDate();
    String newWorkTypeForTeam = scheduleUpdateDto.getWorkType();
    boolean isRestShiftForTeam = "休息".equals(newWorkTypeForTeam);

    if (scheduleDateForTeam != null && newWorkTypeForTeam != null) {
      boolean isRestDayForTeam = false;
      boolean isNationalHolidayForTeam = false;
      try {
        List<String> holidayListForTeam = workCalendarService.queryHolidayList(scheduleDateForTeam, scheduleDateForTeam);
        if (holidayListForTeam != null && holidayListForTeam.size() > 0) {
          isRestDayForTeam = true;
        }
        List<String> nationalHolidayListForTeam = workCalendarService.queryNationalList(scheduleDateForTeam, scheduleDateForTeam);
        if (nationalHolidayListForTeam != null && nationalHolidayListForTeam.size() > 0) {
          isNationalHolidayForTeam = true;
        }
      } catch (Exception e) {
        // 若日历服务异常，保持原流程，不进行拦截
      }

      boolean isWorkdayForTeam = !isRestDayForTeam;

      /*if (isWorkdayForTeam && isRestShiftForTeam && !isNationalHolidayForTeam) {
        result.setCode(ResultCode.FAILURE);
        result.setStatus(1);
        result.setMessage("工作日不能排休息（法定节假日除外）");
        return result;
      }*/

      if (!isWorkdayForTeam && !isRestShiftForTeam) {
        result.setCode(ResultCode.FAILURE);
        result.setStatus(1);
        result.setMessage("周末和法定节假日只能排休息");
        return result;
      }
    }

    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
    BeanUtils.copyProperties(record, scheduleUpdateDto);
    record.setWorkTeam(scheduleUpdateDto.getWorkTeam());
    record.setScheduleDate(scheduleUpdateDto.getScheduleDate());
    scheduleWorkTeamMapper.updateWorkTeamSchedule(record);

    // TODO 同步修改该班组下当天的所有员工的排班
    List<String> empIds = employeeMapper.queryEmpIdByWorkTeam(scheduleUpdateDto.getWorkTeam());
    if (!ObjectUtils.isNull(empIds)) {
      for (String empId : empIds) {
        scheduleMapper.updateEmployeeScheduleByDate(empId, scheduleUpdateDto.getScheduleDate(),
            scheduleUpdateDto.getWorkType());
      }
    }
    result.setMessage("调整排班成功");
    return result;
  }

  /**
   * @Description 根据日期进行调整排班
   * <AUTHOR>
   * @Date 2019/12/2 11:01
   * @Return ResultHelper
   * @Param ScheduleUpdateDto scheduleUpdateDto
   * @Throws Exception
   */
  @Override
  public ResultHelper updateWorkTeamSchedule(ScheduleUpdateDto scheduleUpdateDto) throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
    EmployeeVo employeeVo = employeeMapper.queryEmployeeByEmpId(scheduleUpdateDto.getEmpId());
        /*record.setWorkshop(employeeVo.getWorkshop());
        record.setPlant(employeeVo.getPlant());*/
    record.setWorkTeam(employeeVo.getWorkTeam());
    record.setScheduleDate(scheduleUpdateDto.getScheduleDate());
    scheduleWorkTeamMapper.updateWorkTeamSchedule(record);
    result.setMessage("调整排班成功");
    return result;
  }

  /**
   * @Description H5根据员工号修改排班调整
   * <AUTHOR>
   * @Date 2019/12/2 15:08
   * @Return
   * @Param
   * @Throws Exception
   */
  @Override
  @Transactional
  public ResultHelper updateUserSchedule(ScheduleUpdateDto scheduleUpdateDto) throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    ScheduleEntity record = new ScheduleEntity();
    EmployeeVo employeeVo = employeeMapper.queryEmployeeByEmpId(scheduleUpdateDto.getEmpId());
        /*record.setWorkshop(employeeVo.getWorkshop());
        record.setPlant(employeeVo.getPlant());*/
    record.setWorkTeam(employeeVo.getWorkTeam());
    record.setScheduleDate(scheduleUpdateDto.getScheduleDate());
    record.setEmpId(scheduleUpdateDto.getEmpId());
    record.setWorkType(scheduleUpdateDto.getWorkType());

    String scheduleDate = scheduleUpdateDto.getScheduleDate();
    String newWorkType = scheduleUpdateDto.getWorkType();
    boolean isRestShift = "休息".equals(newWorkType);
    if (scheduleDate != null && newWorkType != null) {
      // 判断是否为休息日（周末或国假）以及是否为法定节假日
      boolean isRestDay = false;
      boolean isNationalHoliday = false;
      try {
        List<String> holidayList = workCalendarService.queryHolidayList(scheduleDate, scheduleDate);
        if (holidayList != null && holidayList.size() > 0) {
          isRestDay = true;
        }
        List<String> nationalHolidayList = workCalendarService.queryNationalList(scheduleDate, scheduleDate);
        if (nationalHolidayList != null && nationalHolidayList.size() > 0) {
          isNationalHoliday = true;
        }
      } catch (Exception e) {
        // 若日历服务异常，保持原流程，不进行拦截
      }

      boolean isWorkday = !isRestDay; // 非休息日即工作日

      // 规则1：工作日不能排休息，除非是法定节假日
      /*if (isWorkday && isRestShift && !isNationalHoliday) {
        result.setCode(ResultCode.FAILURE);
        result.setStatus(1);
        result.setMessage("工作日不能排休息（法定节假日除外）");
        return result;
      }*/

      // 规则2：休息日只能排休息
      if (!isWorkday && !isRestShift) {
        result.setCode(ResultCode.FAILURE);
        result.setStatus(1);
        result.setMessage("周末和法定节假日只能排休息");
        return result;
      }
    }

    scheduleMapper.deleteUserSchedule(record);
    record.setScheduleId(Tools.get32UUID());
    record.setCreateBy(scheduleUpdateDto.getCreateBy());
    record.setSource(1);
    scheduleMapper.addUserSchedule(record);

    //TODO 发消息给员工
    Map<String, String> empMap = empIdAndOpenIdMap(new PageData());
    StringBuffer msg = new StringBuffer();
    msg.append("您的排班被领班修改：").append(scheduleUpdateDto.getScheduleDate())
        .append("，被修改成").append(scheduleUpdateDto.getWorkType()).append("班次");
    wxUtil.sendMessage(empMap.get(scheduleUpdateDto.getEmpId()), msg.toString());

    result.setMessage("调整排班成功");
    return result;
  }

  public Map<String, String> empIdAndOpenIdMap(PageData pd) throws Exception {
    Map<String, String> empMap = new HashMap<>();
    List<Map<String, String>> data = employeeMapper.empIdAndOpenIdMap(pd);
    for (Map<String, String> map : data) {
      empMap.put(map.get("empId"), map.get("openId"));
    }
    return empMap;
  }

  /**
   * @Description 下载排班数据
   * <AUTHOR>
   * @Date 2019/12/11 16:27
   * @Return
   * @Param ScheduleQueryDto scheduleQueryDto
   * @Throws Exception
   */
  @Override
  public Map<String, Object> downloadSchedule(ScheduleQueryDto scheduleQueryDto) throws Exception {
    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
    int year = scheduleQueryDto.getYear();
    int month = scheduleQueryDto.getMonth();
    if (month == 1) {
      month = 12;
      year--;
    } else {
      month--;
    }
    Table table = new Table(1);
    List<List<String>> headList = new ArrayList<>();
    List<String> dateList = DateUtil.getWorkDayOfMonth(year, month);
    List<String> scheduleDateList = DateUtil.getFinanceDate(year, month);
    String startDate = scheduleDateList.get(0);
    String endDate = scheduleDateList.get(1);
        /*dateList.add(0, "厂区");
        dateList.add(1, "车间");*/
    dateList.add(0, "班组");
    for (int i = 0; i < dateList.size(); i++) {
      List<String> hList = new ArrayList<>();
      hList.add(dateList.get(i));
      headList.add(hList);
    }
    table.setHead(headList);
    List<List<String>> dataList = new ArrayList<>();
    BeanUtils.copyProperties(record, scheduleQueryDto);
    Page<ScheduleWorkTeamVo> voPage = scheduleWorkTeamMapper.queryPlanSchedulePage(record);
    if (!ObjectUtils.isNull(voPage)) {
      for (int i = 0; i < voPage.size(); i++) {
        BeanUtils.copyProperties(record, voPage.get(i));
        record.setStartDate(startDate);
        record.setEndDate(endDate);
        List<String> dataList1 = new ArrayList<>();
                /*dataList1.add(voPage.get(i).getPlant());
                dataList1.add(voPage.get(i).getWorkshop());*/
        dataList1.add(record.getWorkTeam());
        List<String> dataList3 = scheduleWorkTeamMapper.queryPlanScheduleWorkTypeList(record);
        if (!ObjectUtils.isNull(dataList3)) {
          dataList1.addAll(dataList3);
          dataList.add(dataList1);
        }
        //voPage.get(i).setDateList();
      }
    }
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put("headList", table);
    dataMap.put("dataList", dataList);
    return dataMap;
  }

  /**
   * @Description 下载员工排班数据
   * <AUTHOR>
   * @Date 2020/4/28 14:06
   * @Return
   * @Param
   * @Throws Exception
   */
  @Override
  public Map<String, Object> downloadEmployeeSchedule(ScheduleQueryDto scheduleQueryDto)
      throws Exception {
    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
    int year = scheduleQueryDto.getYear();
    int month = scheduleQueryDto.getMonth();
    if (month == 1) {
      month = 12;
      year--;
    } else {
      month--;
    }
    Table table = new Table(1);
    List<List<String>> headList = new ArrayList<>();
    List<String> dateList = new ArrayList<>();
    List<String> scheduleDateList = DateUtil.getFinanceDate(year, month);
    String startDate = scheduleDateList.get(0);
    String endDate = scheduleDateList.get(1);
       /* dateList.add("厂区");
        dateList.add("车间");*/
    dateList.add("班组");
    dateList.add("工号");
    dateList.add("姓名");
    List<String> dateList1 = DateUtil.getWorkDayOfMonth(year, month);
    dateList.addAll(dateList1);
    for (int i = 0; i < dateList.size(); i++) {
      List<String> hList = new ArrayList<>();
      hList.add(dateList.get(i));
      headList.add(hList);
    }
    table.setHead(headList);
    List<List<String>> dataList = new ArrayList<>();
    record.setStartDate(startDate);
    record.setEndDate(endDate);
    BeanUtils.copyProperties(record, scheduleQueryDto);
    List<Map<String, String>> dataList3 = scheduleWorkTeamMapper.queryPlanScheduleDateList(record);
    Map<String, String> workTypeMap = transitionWorkType(dataList3);
    Page<EmployeeVo> employeeVos = employeeMapper.queryEmployeePageByWorkTeamAndEmp(
        scheduleQueryDto.getWorkTeam(),scheduleQueryDto.getEmpId(),scheduleQueryDto.getName());
    if (!ObjectUtils.isNull(employeeVos)) {
      record.setWorkTeam(""); //修复班组成员排班和人员排班导出不一致的问题
      List<Map<String, String>> dataList1 = scheduleMapper.queryEmployeePlanScheduleListAll(record);
      Map<String, String> workTypeMap1 = new HashMap<>();
      if (!ObjectUtils.isNull(dataList1)) {
        for (int i = 0; i < dataList1.size(); i++) {
          workTypeMap1.put(dataList1.get(i).get("emp_id") + dataList1.get(i).get("schedule_date"),
              dataList1.get(i).get("work_type"));
        }
      }
      for (int i = 0; i < employeeVos.size(); i++) {
        List<String> dataList2 = new ArrayList<>();
                /*dataList2.add(scheduleQueryDto.getPlant());
                dataList2.add(employeeVos.get(i).getWorkshop());*/
        dataList2.add(employeeVos.get(i).getWorkTeam());
        dataList2.add(employeeVos.get(i).getEmpId());
        dataList2.add(employeeVos.get(i).getName());
        for (int h = 0; h < dateList1.size(); h++) {
          String workType = workTypeMap1.get(employeeVos.get(i).getEmpId() + dateList1.get(h));
          if (ObjectUtils.isNullStr(workType)) {
            workType = workTypeMap.get(dateList1.get(h) + employeeVos.get(i).getWorkTeam());
            if (ObjectUtils.isNullStr(workType)) {
              workType = "";
            }
          }
          dataList2.add(workType);
        }
        dataList.add(dataList2);
      }
    }
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put("headList", table);
    dataMap.put("dataList", dataList);
    return dataMap;
  }

  @Override
  public Map<String, Object> downloadEmployeeScheduleToKayang(
      KayangScheduleQueryDto kayangScheduleQueryDto)
      throws Exception {
    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
    String date = kayangScheduleQueryDto.getDate();
    String[] dateArr = date.split("-");

    int year = Integer.parseInt(dateArr[0]);
    int month = Integer.parseInt(dateArr[1]);
    int day = Integer.parseInt(dateArr[2]);

    List<String> dateList1 = DateUtil.getFinanceDateRange(year, month, day);

    List<String> scheduleDateList = DateUtil.getFinanceDate(year, month, day);
    String startDate = scheduleDateList.get(0);
    String endDate = scheduleDateList.get(1);

    if (day >= 16) {
      if (month == 12) {
        month = 1;
        year++;
      } else {
        month++;
      }
    }
    String monthStr = "";
    if (month < 10) {
      monthStr = "0" + month;
    } else {
      monthStr = month + "";
    }

    String term = year + "-" + monthStr + "-01";

    Table table = new Table(1);
    List<List<String>> headList = new ArrayList<>();
    List<String> dateList = new ArrayList<>();

    dateList.add("期间");
    dateList.add("工号");
    dateList.addAll(dateList1);
    for (int i = 0; i < dateList.size(); i++) {
      List<String> hList = new ArrayList<>();
      hList.add(dateList.get(i));
      headList.add(hList);
    }
    table.setHead(headList);
    List<List<String>> dataList = new ArrayList<>();
    record.setStartDate(startDate);
    record.setEndDate(endDate);
    List<Map<String, String>> dataList3 = scheduleWorkTeamMapper.queryPlanScheduleDateList(record);
    Map<String, String> workTypeMap = transitionWorkType(dataList3);
    Page<EmployeeVo> employeeVos = employeeMapper.queryEmployeePageByWorkTeam("");
    if (!ObjectUtils.isNull(employeeVos)) {
      record.setWorkTeam(""); //修复班组成员排班和人员排班导出不一致的问题
      List<Map<String, String>> dataList1 = scheduleMapper.queryEmployeePlanScheduleListAll(record);
      Map<String, String> workTypeMap1 = new HashMap<>();
      if (!ObjectUtils.isNull(dataList1)) {
        for (int i = 0; i < dataList1.size(); i++) {
          workTypeMap1.put(dataList1.get(i).get("emp_id") + dataList1.get(i).get("schedule_date"),
              dataList1.get(i).get("work_type"));
        }
      }
      for (int i = 0; i < employeeVos.size(); i++) {
        List<String> dataList2 = new ArrayList<>();
        dataList2.add(term);
        dataList2.add(employeeVos.get(i).getEmpId());
        for (int h = 0; h < dateList1.size(); h++) {
          String workType = workTypeMap1.get(employeeVos.get(i).getEmpId() + dateList1.get(h));
          if (ObjectUtils.isNullStr(workType)) {
            workType = workTypeMap.get(dateList1.get(h) + employeeVos.get(i).getWorkTeam());
            if (ObjectUtils.isNullStr(workType)) {
              workType = "";
              break;
            }
          }
          dataList2.add(workType);
        }
        // 过滤排班为空的数据
        if (dataList2.size() > 2) {
          dataList.add(dataList2);
        }
//        dataList.add(dataList2);
      }
    }
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put("headList", table);
    dataMap.put("dataList", dataList);
    return dataMap;
  }

  /**
   * @Description 根据日期查询班组和人员排班
   * <AUTHOR>
   * @Date 2020/4/23 15:29
   * @Return
   * @Param
   * @Throws Exception
   */
  @Override
  public List<Map<String, String>> queryScheduleByDate(String scheduleDate) throws Exception {
    List<Map<String, String>> dataList = new ArrayList<>();
    List<ScheduleWorkTeamVo> scheduleWorkTeamVos = scheduleWorkTeamMapper.queryWorkTeamScheduleByDate(
        scheduleDate);
    Map<String, String> workTypeMap = new HashMap<>();
    if (!ObjectUtils.isNull(scheduleWorkTeamVos)) {
      for (int i = 0; i < scheduleWorkTeamVos.size(); i++) {
        workTypeMap.put(scheduleWorkTeamVos.get(i).getWorkTeam(),
            scheduleWorkTeamVos.get(i).getWorkType());
      }
    }
    dataList.add(workTypeMap);
    Map<String, String> workTypeMap1 = new HashMap<>();
    List<Map<String, String>> employeeList = scheduleMapper.queryEmployeeScheduleByDate(
        scheduleDate);
    if (!ObjectUtils.isNull(employeeList)) {
      for (int i = 0; i < employeeList.size(); i++) {
        String empId = employeeList.get(i).get("empId");
        String workType = employeeList.get(i).get("workType");
        workTypeMap1.put(empId, workType);
      }
    }
    dataList.add(workTypeMap1);
    return dataList;
  }


  /**
   * @Description 导入排班数据
   * <AUTHOR>
   * @Date 2019/12/12 15:54
   * @Return
   * @Param
   * @Throws Exception
   */
  @Override
  @Transactional
  public ResultHelper importSchedule(List<Object> dataList, String username) throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    if (ObjectUtils.isNull(dataList)) {
      result.setCode(ResultCode.FAILURE);
      result.setStatus(1);
      result.setMessage("数据表格解析为空，请检查数据表格");
      return result;
    }
    Map<String, Map<String, String>> dataMap = new HashMap<>();
    List<WorkGroupEntity> workGroupList = workGroupMapper.queryWorkGroupAll();
    if (ObjectUtils.isNull(workGroupList)) {
      result.setCode(ResultCode.FAILURE);
      result.setStatus(1);
      result.setMessage("请先维护班组信息");
      return result;
    }
    Map<String, String> workGroupMap = new HashMap<>();
    for (int i = 0; i < workGroupList.size(); i++) {
      String validType = workGroupList.get(i).getValidWorkType();
      if (!ObjectUtils.isNullStr(validType)) {
        String key = workGroupList.get(i).getWorkTeam();
        workGroupMap.put(key, validType);
      }
    }
    List<String> dateList = new ArrayList<>();
    Set<String> dateSet = new LinkedHashSet<>();
    for (int i = 0; i < dataList.size(); i++) {
      List<String> dataList1 = (List<String>) dataList.get(i);
      if (i == 0) {
        for (int h = 0; h < dataList1.size(); h++) {
          if (h > 0) {
            String date = dataList1.get(h);
            if (DateUtil.isValidDate(date)) {
              dateSet.add(date);//用set集合自动去重
            } else {
              if (!ObjectUtils.isNullStr(date)) {
                result.setStatus(1);
                result.setCode(ResultCode.FAILURE);
                result.setMessage("第" + (h + 1) + "列日期不合法");
                return result;
              }
            }
          }
        }
        dateList = new ArrayList<>(dateSet);
      }
      if (i != 0) {
        Map<String, String> dataMap1 = new HashMap<>();
        String key = dataList1.get(0);
        if (!workGroupMap.containsKey(key)) {
          result.setCode(ResultCode.FAILURE);
          result.setStatus(1);
          result.setMessage(
              "班组信息错误，请检查班组信息是否已经维护(" + "班组：" + dataList1.get(0) + ")");
          return result;
        }
        //dataMap1.put(dateList.get)
        for (int h = 0; h < dataList1.size() - 1; h++) {
          String workType = dataList1.get(h + 1);
          if (!ObjectUtils.isNullStr(workType)) {
            String validWorkType = workGroupMap.get(key);
            List<String> validWorkTypeList = new ArrayList<>();
            if (!ObjectUtils.isNullStr(validWorkType)) {
              validWorkTypeList.addAll(Arrays.asList(validWorkType.split("\\|")));
            }
            if (!validWorkTypeList.contains(workType)) {
              result.setCode(ResultCode.FAILURE);
              result.setStatus(1);
              result.setMessage("班组：" + key.split(";")[0] + " 日期:" + dateList.get(h) + " 班次："
                  + dataList1.get(h + 1) + "排班不是可选班次");
              return result;
            }
            dataMap1.put(dateList.get(h), workType);
          }
        }
        if (dataMap.containsKey(key)) {
          result.setCode(ResultCode.FAILURE);
          result.setStatus(1);
          result.setMessage("班组重复，请检查表格");
          return result;
        }
        dataMap.put(key, dataMap1);
      }
    }
    Page<ScheduleWorkTeamVo> voPage = scheduleWorkTeamMapper.queryScheduleByDateList(dateList);
    List<ScheduleWorkTeamEntity> addList = new ArrayList<>();
    List<ScheduleWorkTeamEntity> updateList = new ArrayList<>();
    if (!ObjectUtils.isNull(voPage)) {
      for (int i = 0; i < voPage.size(); i++) {
        String key = voPage.get(i).getWorkTeam();
        if (dataMap.containsKey(key)) {
          Map<String, String> paramMap = dataMap.get(key);
          ScheduleWorkTeamEntity schedule = new ScheduleWorkTeamEntity();
          String workType = paramMap.get(voPage.get(i).getScheduleDate());
          if (!ObjectUtils.isNullStr(workType)) {
            schedule.setWorkType(workType);//获取班组对应日期的班次
          } else {
            continue;
          }
          schedule.setScheduleDate(voPage.get(i).getScheduleDate());
                    /*schedule.setPlant(voPage.get(i).getPlant());
                    schedule.setWorkshop(voPage.get(i).getWorkshop());*/
          schedule.setWorkTeam(voPage.get(i).getWorkTeam());
          schedule.setUpdateBy(username);
          updateList.add(schedule);
          paramMap.remove(voPage.get(i).getScheduleDate());
        }
      }
    }
    for (String str : dataMap.keySet()) {
      Map<String, String> paramMap = dataMap.get(str);
      String[] strArray = str.split(";");
      String workTeam = strArray[0];
      for (String date : paramMap.keySet()) {
        if (ObjectUtils.isNullStr(date)) {
          continue;
        }
        ScheduleWorkTeamEntity schedule = new ScheduleWorkTeamEntity();
        schedule.setWorkType(paramMap.get(date));//获取班组对应日期的班次
        schedule.setScheduleDate(date);
                /*schedule.setPlant(plant);
                schedule.setWorkshop(workshop);*/
        schedule.setWorkTeam(workTeam);
        schedule.setScheduleWorkTeamId(Tools.get32UUID());
        schedule.setCreateBy(username);
        addList.add(schedule);
      }
    }
    if (!ObjectUtils.isNull(addList)) {
      if (addList.size() > 50) {
        int size = addList.size();
        for (int i = 0; i < addList.size(); i += 50) {
          if (i + 50 > size) {
            scheduleWorkTeamMapper.addScheduleWorkTeamList(addList.subList(i, size));
            scheduleMapper.deleteScheduleList(addList.subList(i, size));
          } else {
            scheduleWorkTeamMapper.addScheduleWorkTeamList(addList.subList(i, i + 50));
            scheduleMapper.deleteScheduleList(addList.subList(i, i + 50));
          }
        }
      } else {
        scheduleWorkTeamMapper.addScheduleWorkTeamList(addList);
        scheduleMapper.deleteScheduleList(addList);
      }
    }
    if (!ObjectUtils.isNull(updateList)) {
      if (updateList.size() > 50) {
        int size = updateList.size();
        for (int i = 0; i < updateList.size(); i += 50) {
          if (i + 50 > size) {
            scheduleWorkTeamMapper.updateScheduleWorkTeamList(updateList.subList(i, size));
            scheduleMapper.deleteScheduleList(updateList.subList(i, size));
          } else {
            scheduleWorkTeamMapper.updateScheduleWorkTeamList(updateList.subList(i, i + 50));
            scheduleMapper.deleteScheduleList(updateList.subList(i, i + 50));
          }
        }
      } else {
        scheduleWorkTeamMapper.updateScheduleWorkTeamList(updateList);
        scheduleMapper.deleteScheduleList(updateList);
      }
    }
    result.setMessage("排班导入成功");
    return result;
  }

  /**
   * @Description 导入员工具体排班数据(先删除所有对应的班组员工排班, 再同步添加进去 ， 用Spring事务控制)
   * <AUTHOR>
   * @Date 2020/4/28 15:41
   * @Return
   * @Param
   * @Throws Exception
   */
  @Override
  @Transactional
  public ResultHelper importEmployeeSchedule(List<Object> dataList, String username)
      throws Exception {
    WorkGroupEntity workGroupEntity = new WorkGroupEntity();
    ResultHelper result = ResultHelper.builder().build();
    if (ObjectUtils.isNull(dataList) || dataList.size() < 2) {
      result.setCode(ResultCode.FAILURE);
      result.setStatus(1);
      result.setMessage("导入数据不可为空");
      return result;
    }
    List<String> dataList1 = (List<String>) dataList.get(1);
    if (dataList1.size() < 31) {
      result.setCode(ResultCode.FAILURE);
      result.setStatus(1);
      result.setMessage("导入数据不合法");
      return result;
    }
        /*String plant = dataList1.get(0);
        String workshop = dataList1.get(1);*/
    String workTeam = dataList1.get(0);
        /*workGroupEntity.setPlant(plant);
        workGroupEntity.setWorkshop(workshop);*/
    workGroupEntity.setWorkTeam(workTeam);
    workGroupEntity.setName(workTeam);
    WorkGroupVo workGroupVo = workGroupMapper.queryWorkGroupDetailByName(workGroupEntity);
    if (ObjectUtils.isNull(workGroupVo)) {
      result.setCode(ResultCode.FAILURE);
      result.setStatus(1);
      result.setMessage("未查询到对应的有效班组信息，请检查");
      return result;
    }
    List<String> dataList0 = (List<String>) dataList.get(0);
    List<String> dateList = new ArrayList<>();
    String validWorkType = workGroupVo.getValidWorkType();//获取可添加班次
    List<String> validWorkTypeList = new ArrayList<>();
    if (!ObjectUtils.isNullStr(validWorkType)) {
      validWorkTypeList.addAll(Arrays.asList(validWorkType.split("\\|")));
    }
    Set<String> empSet = new HashSet<>();
    for (int i = 3; i < dataList0.size(); i++) {
      if (!ObjectUtils.isNullStr(dataList0.get(i))) {
        dateList.add(dataList0.get(i));
      } else {
        continue;
      }
      if (!DateUtil.isValidDate(dataList0.get(i))) {
        result.setStatus(1);
        result.setCode(ResultCode.FAILURE);
        result.setMessage("第" + (i + 1) + "列日期不合法");
        return result;
      }
    }
    List<ScheduleEntity> addList = new ArrayList<>();
    for (int i = 1; i < dataList.size(); i++) {
      List<String> dataList2 = (List<String>) dataList.get(i);

      if (ObjectUtils.isNull(dataList2) || dataList2.size() != dataList1.size()) {
        result.setStatus(1);
        result.setCode(ResultCode.FAILURE);
        result.setMessage("第" + (i + 1) + "行数据不完整");
        return result;
      }
      String empId = dataList2.get(1);
      if (empSet.contains(empId)) {
        result.setStatus(1);
        result.setCode(ResultCode.FAILURE);
        result.setMessage("第" + (i + 1) + "行工号重复");
        return result;
      }
      empSet.add(empId);
      for (int h = 0; h < dateList.size(); h++) {
        /*if (!validWorkTypeList.contains(dataList2.get(h+3)) && !"休息".equals(dataList2.get(h+3))) {*/
        if (!validWorkTypeList.contains(dataList2.get(h + 3))) {
          result.setStatus(1);
          result.setCode(ResultCode.FAILURE);
          result.setMessage("第" + (i + 1) + "行，第" + (h + 4) + "列班次不是可选班次");
          return result;
        }
        ScheduleEntity record = new ScheduleEntity();
                /*record.setWorkshop(workshop);
                record.setPlant(plant);*/
        record.setWorkTeam(workTeam);
        record.setScheduleDate(dateList.get(h));
        record.setEmpId(empId);
        record.setWorkType(dataList2.get(h + 3));
        record.setCreateBy(username);
        record.setScheduleId(Tools.get32UUID());
        addList.add(record);
      }
    }
    ScheduleWorkTeamEntity record = new ScheduleWorkTeamEntity();
    record.setWorkTeam(workTeam);
        /*record.setPlant(plant);
        record.setWorkshop(workshop);*/
    record.setStartDate(dateList.get(0));
    record.setEndDate(dateList.get(dateList.size() - 1));
    record.setEmpIdList(new ArrayList<>(empSet));
    //删除该班组当前财务月月份的员工排班
    scheduleMapper.deleteScheduleByMonth(record);
    addEmployeeScheduleList(addList);
    result.setStatus(0);
    result.setMessage("班次导入成功");
    return result;
  }

  @Override
  public void employeeSchedulingRestReminderToSupervisor() throws Exception {
    // 获取下一天
    String scheduleDate = DateUtil.getOffDayDate("1");
    List<ScheduleRestDto> scheduleRestDtoList = scheduleMapper.listSchedulingRestEmployees(
        scheduleDate);
    Map<String, String> empMap = employeeService.empIdAndOpenIdMap(new PageData());
    Map param = new HashMap();
    for (ScheduleRestDto item : scheduleRestDtoList) {
      param = new HashMap();
      String employeeNames = employeeService.queryEmpNamesByEmpIds(item.getEmpIds());
      param.put("workTeam", item.getWorkTeam());
      param.put("date", scheduleDate);
      param.put("employees", employeeNames);

      String content = wxUtil.getMsgContentByType(MessageType.REST_REMINDER, param, "",
          empMap.get(item.getLeaderId()));
      wxUtil.sendMessage(empMap.get(item.getLeaderId()), content);

//      wxCpMessageService.sendMsg(empMap.get(item.getLeaderId()), param, "",
//          MessageType.REST_REMINDER);
    }

  }

  /**
   * @Description 批量添加
   * <AUTHOR>
   * @Date 2020/4/28 17:50
   * @Return
   * @Param
   * @Throws Exception
   */
  private void addEmployeeScheduleList(List<ScheduleEntity> dataList) throws Exception {
    if (!ObjectUtils.isNull(dataList)) {
      int size = dataList.size();
      if (size > 50) {
        for (int i = 0; i < size; i += 50) {
          if (i + 50 > size) {
            scheduleMapper.addScheduleList(dataList.subList(i, size));
          } else {
            scheduleMapper.addScheduleList(dataList.subList(i, i + 50));
          }
        }
      } else {
        scheduleMapper.addScheduleList(dataList);
      }
    }
  }

  private LocalTime convertString2LocalTime(String time) {
    if (ObjectUtils.isNullStr(time)) {
      return null;
    }
    String[] timeArr = time.split(":");
    if (timeArr.length != 3) {
      return null;
    }
    return LocalTime.of(Integer.parseInt(timeArr[0]), Integer.parseInt(timeArr[1]),
        Integer.parseInt(timeArr[2]));
  }

  @Override
  public void sendMessageToSupervisorAboutModifiedEmployeeSchedules() throws Exception {
    String changeDate = DateUtil.addDayToDate(DateUtil.getDay(), -1);
    String startTime = DateUtil.getDateStart(changeDate);
    String endTime = DateUtil.getDateEnd(changeDate);
    List<ScheduleChangeDto> data = scheduleMapper.listSchedulingChangeEmployees(startTime, endTime);
    Map<String, String> empMap = employeeService.empIdAndOpenIdMap(new PageData());
    String detail = REQUESTURL + "/#/scheduling-change?changeDate=" + changeDate;
    for (ScheduleChangeDto item : data) {
      String detail_url = detail + "&leaderId=" + item.getLeaderId();
      // 获取领班的上级
      EmployeeVo employeeVo = (EmployeeVo) employeeService.queryEmployeeDetail(item.getLeaderId())
          .getData();

      if (!StringUtils.isEmpty(employeeVo.getFirstLevelMgrId()) && !StringUtils.isEmpty(
          empMap.get(employeeVo.getFirstLevelMgrId()))) {
        StringBuffer msg = new StringBuffer();
        msg.append(changeDate).append(",")
            .append(item.getWorkGroup()).append("班组共有")
            .append(item.getCount()).append("条排班被修改，点击(").append(detail_url)
            .append(")查看详情");
        wxUtil.sendMessage(empMap.get(employeeVo.getFirstLevelMgrId()), msg.toString());
      }
    }
  }

  @Override
  public ResultHelper listEmployeeWhichChangeSchedulesBySupervisor(String leaderId,
      String changeDate)
      throws Exception {
    ResultHelper resultHelper = ResultHelper.builder().build();
    String startTime = DateUtil.getDateStart(changeDate);
    String endTime = DateUtil.getDateEnd(changeDate);
    List<ScheduleChangeDetailDto> data = scheduleMapper.listSchedulingChangeDetailEmployees(
        leaderId, startTime, endTime);
    resultHelper.setData(data);

    return resultHelper;
  }
}
