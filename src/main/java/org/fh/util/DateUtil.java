package org.fh.util;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 说明：日期处理 作者：Admin 官网：www.rinsys.com
 */
public class DateUtil {

  private final static SimpleDateFormat sdfYear = new SimpleDateFormat("yyyy");
  private final static SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd");
  private final static SimpleDateFormat sdfDays = new SimpleDateFormat("yyyyMMdd");
  private final static SimpleDateFormat sdfTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
  private final static SimpleDateFormat sdfTimes = new SimpleDateFormat("yyyyMMddHHmmss");

  private final static SimpleDateFormat scdfDay = new SimpleDateFormat("yyyy年MM月dd日");


  static long minTime = Timestamp.valueOf("1970-01-01 09:00:00").getTime();
  static long maxTime = Timestamp.valueOf("2038-01-19 11:00:00").getTime();

  /**
   * 获取YYYY格式
   *
   * @return
   */
  public static String getSdfTimes() {
    return sdfTimes.format(new Date());
  }

  /**
   * 获取YYYY格式
   *
   * @return
   */
  public static String getYear() {
    return sdfYear.format(new Date());
  }

  /**
   * 获取YYYY-MM-DD格式
   *
   * @return
   */
  public static String getDay() {
    return sdfDay.format(new Date());
  }

  public static String getChinaDay() {
    return scdfDay.format(new Date());
  }

  /**
   * 获取YYYYMMDD格式
   *
   * @return
   */
  public static String getDays() {
    return sdfDays.format(new Date());
  }

  /**
   * 获取YYYY-MM-DD HH:mm:ss格式
   *
   * @return
   */
  public static String getTime() {
    return sdfTime.format(new Date());
  }

  /**
   * @param s
   * @param e
   * @return boolean
   * @throws
   * @Title: compareDate
   * @Description: TODO(日期比较 ， 如果s > = e 返回true 否则返回false)
   * <AUTHOR>
   */
  public static boolean compareDate(String s, String e) {
    if (fomatDate(s) == null || fomatDate(e) == null) {
      return false;
    }
    return fomatDate(s).getTime() >= fomatDate(e).getTime();
  }

  /**
   * 格式化日期
   *
   * @return
   */
  public static Date fomatDate(String date) {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
    try {
      return fmt.parse(date);
    } catch (ParseException e) {
      e.printStackTrace();
      return null;
    }
  }

  /**
   * @Description 获取日期里的年份
   * <AUTHOR>
   * @Date 2019/11/13 16:02
   * @Return
   * @Param
   * @Throws Exception
   */
  public static int getYear(Date date) {
    Calendar c = Calendar.getInstance();
    int dateStr = c.get(Calendar.YEAR);
    return 0;
  }

  /**
   * 校验日期是否合法
   *
   * @return
   */
  public static boolean isValidDate(String s) {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
    try {
      fmt.parse(s);
      return true;
    } catch (Exception e) {
      return false; // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
    }
  }


  public static boolean checkValidDateTime(String s) {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    try {
      fmt.parse(s);
      return true;
    } catch (Exception e) {
      return false; // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
    }
  }

  /**
   * @param startTime
   * @param endTime
   * @return
   */
  public static int getDiffYear(String startTime, String endTime) {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
    try {
      int years = (int) (
          ((fmt.parse(endTime).getTime() - fmt.parse(startTime).getTime()) / (1000 * 60 * 60 * 24))
              / 365);
      return years;
    } catch (Exception e) {
      return 0;    // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
    }
  }

  /**
   * <li>功能描述：时间相减得到天数
   *
   * @param beginDateStr
   * @param endDateStr
   * @return long
   * <AUTHOR>
   */
  public static int getDaySub(String beginDateStr, String endDateStr) {
    int day = 0;
    java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd");
    java.util.Date beginDate = null;
    java.util.Date endDate = null;
    try {
      beginDate = format.parse(beginDateStr);
      endDate = format.parse(endDateStr);
    } catch (ParseException e) {
      e.printStackTrace();
    }
    day = (int) ((endDate.getTime() - beginDate.getTime()) * 1 / (24 * 60 * 60 * 1000));
    //System.out.println("相隔的天数="+day);
    return day;
  }

  /**
   * 得到n天之后的日期
   *
   * @param days
   * @return
   */
  public static String getAfterDayDate(String days) {
    int daysInt = Integer.parseInt(days);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.DATE, daysInt); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    String dateStr = sdfd.format(date);
    return dateStr;
  }

  public static String getAfterDayDate(String dateStr, int days) throws Exception {
    Calendar canlendar = Calendar.getInstance(); // java.util包
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd");
    canlendar.setTime(sdfd.parse(dateStr));
    canlendar.add(Calendar.DATE, days); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();
    return sdfd.format(date);
  }

  /**
   * @Description 获取多少分钟之后的时间
   * <AUTHOR>
   * @Date 2019/12/1 23:24
   * @Return String
   * @Param
   * @Throws Exception
   */
  public static String getAfterMinuteDate(String minute) {
    int minuteInt = Integer.parseInt(minute);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.MINUTE, minuteInt); //
    Date date = canlendar.getTime();
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    String dateStr = sdfd.format(date);
    return dateStr;
  }

  /**
   * @Description 获取多少分钟之后的时间
   * <AUTHOR>
   * @Date 2020/5/14 9:31
   * @Return
   * @Param
   * @Throws Exception
   */
  public static String getAfterMinuteDate(String dateStr, String minute) throws Exception {
    int minuteInt = Integer.parseInt(minute);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    canlendar.setTime(sdfd.parse(dateStr));
    canlendar.add(Calendar.MINUTE, minuteInt); //
    Date date = canlendar.getTime();
    //SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    dateStr = sdfd.format(date);
    return dateStr;
  }

  /**
   * @Description 获取多少天之后的日期
   * <AUTHOR>
   * @Date 2019/12/1 10:23
   * @Return
   * @Param String days 天数
   * @Throws Exception
   */
  public static String getOffDayDate(String days) {
    int daysInt = Integer.parseInt(days);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.DATE, daysInt); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd");
    String dateStr = sdfd.format(date);
    return dateStr;
  }

  /**
   * @Description 根据一个日期获取这个日期之后多少天之后的日期
   * <AUTHOR>
   * @Date 2019/12/1 12:44
   * @Return String
   * @Param String date 初始日期 String days 多少天之后
   * @Throws Exception
   */
  public static String getOffDayDate(String date, String days) throws Exception {
    int year = Integer.parseInt(date.substring(0, 4));
    int month = Integer.parseInt(date.substring(5, 7));
    int day = Integer.parseInt(date.substring(8, 10));
    int daysInt = Integer.parseInt(days);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.set(year, month - 1, day);
    canlendar.add(Calendar.DATE, daysInt); // 日期减 如果不够减会将月变动
    //Date newDate = canlendar.getTime();
    //String dateStr = sdfd.format(newDate);
    month = (canlendar.get(Calendar.MONTH) + 1);
    String months = "" + month;
    if (month < 10) {
      months = "0" + month;
    }
    day = canlendar.get(Calendar.DAY_OF_MONTH);
    if (day < 10) {
      days = "0" + day;
    } else {
      days = String.valueOf(day);
    }
    String dateStr = "" + canlendar.get(Calendar.YEAR) + "-" + months + "-" + days;
    return dateStr;
  }

  /**
   * @Description 获取n月之后的某天
   * <AUTHOR>
   * @Date 2019/10/30 12:42
   * @Return
   * @Param
   * @Throws Exception
   */
  public static String getAfterMonthDate(String month) {
    int monthInt = Integer.parseInt(month);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.MONTH, monthInt); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    String dateStr = sdfd.format(date);
    return dateStr;
  }

  /**
   * @Description 获取n月之后的某月
   * <AUTHOR>
   * @Date 2019/12/17 11:00
   * @Return
   * @Param
   * @Throws Exception
   */
  public static String getAfterMonthToMonth(String month) {
    int monthInt = Integer.parseInt(month);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.MONTH, monthInt); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM");
    String dateStr = sdfd.format(date);
    return dateStr;
  }

  /**
   * @Description 获取n月之后的某天(年月日)
   * <AUTHOR>
   * @Date 2019/12/17 11:00
   * @Return
   * @Param
   * @Throws Exception
   */
  public static String getAfterMonthToDay(String month) {
    int monthInt = Integer.parseInt(month);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.MONTH, monthInt); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd");
    String dateStr = sdfd.format(date);
    return dateStr;
  }

  /**
   * 得到n天之后是周几
   *
   * @param days
   * @return
   */
  public static String getAfterDayWeek(String days) {
    int daysInt = Integer.parseInt(days);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.DATE, daysInt); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();
    SimpleDateFormat sdf = new SimpleDateFormat("E");
    String dateStr = sdf.format(date);
    return dateStr;
  }

  /**
   * @Description 获取当前星期几
   * <AUTHOR>
   * @Date 2019/12/2 11:26
   * @Return
   * @Param
   * @Throws Exception
   */
  public static String getWeekDay() {
    Calendar canlendar = Calendar.getInstance(); // java.util包
    Date date = canlendar.getTime();
    SimpleDateFormat sdf = new SimpleDateFormat("E");
    String dateStr = sdf.format(date);
    return dateStr;
  }

  /**
   * @Description 判断传进来的日期字符串是否是周末
   * <AUTHOR>
   * @Date 2019/11/13 15:43
   * @Return boolean
   * @Param String day
   * @Throws Exception
   */
  public static boolean checkDayOfWeekend(String day) throws Exception {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
    Date date = fmt.parse(day);
    SimpleDateFormat sdf = new SimpleDateFormat("E");
    String dateStr = sdf.format(date);
    System.out.println("日期星期：" + dateStr);
    if ("星期六".equalsIgnoreCase(dateStr) || "星期日".equalsIgnoreCase(dateStr)) {
      return true;
    }
    return false;
  }

  /**
   * @Description 根据Long值转换成字符串日期
   * <AUTHOR>
   * @Date 2019/5/22 13:18
   * @Return str
   * @Param Long date
   * @Throws Exception
   */
  public static String formartDate(Long date) {
    SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date newDate = new Date(date);
    return fmt.format(newDate);
  }

  /**
   * @Description 根据Date转换成时间字符串
   * <AUTHOR>
   * @Date 2019/5/22 13:21
   * @Return string
   * @Param Date date
   * @Throws Exception
   */
  public static String formartDate(Date date) {
    SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    return fmt.format(date);
  }

  public static String getDateTime(String ditNumber) {
    //如果不是数字
    //如果是数字 小于0则 返回
    BigDecimal bd = new BigDecimal(ditNumber);
    int days = bd.intValue();//天数
    int mills = (int) Math.round(bd.subtract(new BigDecimal(days)).doubleValue() * 24 * 3600);
    //获取时间
    Calendar c = Calendar.getInstance();
    c.set(1900, 0, 1);
    c.add(Calendar.DATE, days - 2);
    int hour = mills / 3600;
    int minute = (mills - hour * 3600) / 60;
    int second = mills - hour * 3600 - minute * 60;
    c.set(Calendar.HOUR_OF_DAY, hour);
    c.set(Calendar.MINUTE, minute);
    c.set(Calendar.SECOND, second);
    Date d = c.getTime();//Date
    return formartDate(d);
  }

  /**
   * 按照yyyy-MM-dd HH:mm:ss的格式，日期转字符串
   *
   * @param date
   * @return yyyy-MM-dd HH:mm:ss
   */
  public static String date2Str(Date date) {
    return date2Str(date, "yyyy-MM-dd HH:mm:ss");
  }


  /**
   * @Description 获取当期时间字符串(yyyyMMddHHmmss)
   * <AUTHOR>
   * @Date 2019/4/24 9:09
   * @Return String
   * @Throws Exception
   */
  public static String getNowDateStr() {
    SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
    return df.format(new Date());
  }

  public static String getNowTimeStr() {
    String time = getNowDateStr();
    String nowTime =
        time.substring(0, 4) + "年" + time.substring(4, 6) + "月" + time.substring(6, 8) + "日"
            + time.substring(8, 10) + "时" + time.substring(10, 12) + "分" + time.substring(12, 14)
            + "秒";
    return nowTime;
  }

  /**
   * 按照yyyy-MM-dd HH:mm:ss的格式，字符串转日期
   *
   * @param date
   * @return
   */
  public static Date str2Date(String date) {
    if (Tools.notEmpty(date)) {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      try {
        return sdf.parse(date);
      } catch (ParseException e) {
        e.printStackTrace();
      }
      return new Date();
    } else {
      return null;
    }
  }

  public static String getTimeStrByDate(String dateTime) {
    if (dateTime.length() == 19) {
      int hour = Integer.parseInt(dateTime.substring(11, 13));
      int minute = Integer.parseInt(dateTime.substring(14, 16));
      dateTime = dateTime.substring(0, 10);
      if (hour < 10) {
        dateTime = dateTime + " 0" + hour;
      } else {
        dateTime = dateTime + " " + hour;
      }
      if (minute < 10) {
        dateTime = dateTime + ":0" + minute;
      } else {
        dateTime = dateTime + ":" + minute;
      }
      dateTime += ":00";
    }
    return dateTime;
  }

  /**
   * 把时间根据时、分、秒转换为时间段
   *
   * @param StrDate
   */
  public static String getTimes(String StrDate) {
    String resultTimes = "";
    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    java.util.Date now;
    try {
      now = new Date();
      java.util.Date date = df.parse(StrDate);
      long times = now.getTime() - date.getTime();
      long day = times / (24 * 60 * 60 * 1000);
      long hour = (times / (60 * 60 * 1000) - day * 24);
      long min = ((times / (60 * 1000)) - day * 24 * 60 - hour * 60);
      long sec = (times / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);

      StringBuffer sb = new StringBuffer();
      //sb.append("发表于：");
      if (hour > 0) {
        sb.append(hour + "小时前");
      } else if (min > 0) {
        sb.append(min + "分钟前");
      } else {
        sb.append(sec + "秒前");
      }
      resultTimes = sb.toString();
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return resultTimes;
  }

  /**
   * @Description 根据年月获取这个月所有的天数
   * <AUTHOR>
   * @Date 2019/7/15 17:47
   * @Return
   * @Param
   * @Throws Exception
   */
  public static List<String> getDayByMonth(int yearParam, int monthParam) {
    List list = new ArrayList();
    Calendar aCalendar = Calendar.getInstance(Locale.CHINA);
    aCalendar.set(yearParam, monthParam - 1, 1);
    int year = aCalendar.get(Calendar.YEAR);//年份
    int month = aCalendar.get(Calendar.MONTH) + 1;//月份
    int day = aCalendar.getActualMaximum(Calendar.DATE);
    list.addAll(createDate(year, month, 1, day));
    return list;
  }

  /**
   * @Description 生成本月16号到下个月15号所有的日期
   * <AUTHOR>
   * @Date 2019/11/30 11:23
   * @Return List<String>
   * @Param
   * @Throws Exception
   */
  public static List<String> getWorkDayOfMonth(int yearParam, int monthParam) {
    List list = new ArrayList();
    Calendar aCalendar = Calendar.getInstance(Locale.CHINA);
    monthParam--;
    aCalendar.set(yearParam, monthParam, 1);
    int year = aCalendar.get(Calendar.YEAR);//年份
    int month = aCalendar.get(Calendar.MONTH) + 1;//月份
    int day = aCalendar.getActualMaximum(Calendar.DATE);
    list.addAll(createDate(year, month, 16, day));
    if (month == 12) {//若是生成12月份的数据，会关联到下一年
      yearParam++;
      month = 1;//过年之后使用1月
      list.addAll(createDate(yearParam, month, 1, 15));
    } else {
      month++;//下一个月
      list.addAll(createDate(year, month, 1, 15));
    }
    return list;
  }

  /**
   * @Description 生成2个财务月的日期范围（从当前财务月16号到下下个月15号）
   * <AUTHOR> Assistant
   * @Date 2024/12/26
   * @Return List<String>
   * @Param yearParam 年份, monthParam 月份
   * @Throws Exception
   */
  public static List<String> getWorkDayOfTwoMonths(int yearParam, int monthParam) {
    List<String> list = new ArrayList<>();
    Calendar aCalendar = Calendar.getInstance(Locale.CHINA);
    monthParam--;
    aCalendar.set(yearParam, monthParam, 1);
    int year = aCalendar.get(Calendar.YEAR);//年份
    int month = aCalendar.get(Calendar.MONTH) + 1;//月份
    int day = aCalendar.getActualMaximum(Calendar.DATE);
    
    // 第一个财务月：从当前月16号到下月15号
    list.addAll(createDate(year, month, 16, day));
    if (month == 12) {//若是生成12月份的数据，会关联到下一年
      year++;
      month = 1;//过年之后使用1月
      list.addAll(createDate(year, month, 1, 15));
      // 第二个财务月：从1月16号到2月15号
      list.addAll(createDate(year, month, 16, 31));
      month = 2;
      list.addAll(createDate(year, month, 1, 15));
    } else {
      month++;//下一个月
      list.addAll(createDate(year, month, 1, 15));
      // 第二个财务月：从下月16号到下下月15号
      Calendar nextCalendar = Calendar.getInstance(Locale.CHINA);
      nextCalendar.set(year, month - 1, 1); // month-1 因为Calendar月份从0开始
      int dayNext = nextCalendar.getActualMaximum(Calendar.DATE);
      list.addAll(createDate(year, month, 16, dayNext));
      if (month == 12) {//如果下月是12月，下下月就是次年1月
        year++;
        month = 1;
      } else {
        month++;//下下个月
      }
      list.addAll(createDate(year, month, 1, 15));
    }
    return list;
  }

  public static List<String> getFinanceDateRange(int year, int month, int day) {
    LocalDate inputDate = LocalDate.of(year, month, day);
    LocalDate startDate, endDate;

    if (day < 16) {
      startDate = inputDate.minusMonths(1).withDayOfMonth(16);
      endDate = inputDate.withDayOfMonth(15);
    } else {
      startDate = inputDate.withDayOfMonth(16);
      endDate = inputDate.plusMonths(1).withDayOfMonth(15);
    }

    return getDatesBetween(startDate, endDate);
  }

  public static List<String> getFinanceDateRange() {
    LocalDate currentDate = LocalDate.now();
    LocalDate startDate, endDate;

    if (currentDate.getDayOfMonth() < 16) {
      startDate = currentDate.minusMonths(1).withDayOfMonth(16);
      endDate = currentDate.withDayOfMonth(15);
    } else {
      startDate = currentDate.withDayOfMonth(16);
      endDate = currentDate.plusMonths(1).withDayOfMonth(15);
    }

    return getDatesBetween(startDate, endDate);
  }

  public static List<String> getDatesBetween(LocalDate startDate, LocalDate endDate) {
    List<String> dates = new ArrayList<>();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    while (!startDate.isAfter(endDate)) {
      dates.add(startDate.format(formatter));
      startDate = startDate.plusDays(1);
    }

    return dates;
  }

  public static List<String> createDate(int year, int month, int count, int maxDay) {
    List list = new ArrayList();
    for (int i = count; i <= maxDay; i++) {
      String aDate = null;
      if (month < 10 && i < 10) {
        aDate = String.valueOf(year) + "-0" + month + "-0" + i;
      }
      if (month < 10 && i >= 10) {
        aDate = String.valueOf(year) + "-0" + month + "-" + i;
      }
      if (month >= 10 && i < 10) {
        aDate = String.valueOf(year) + "-" + month + "-0" + i;
      }
      if (month >= 10 && i >= 10) {
        aDate = String.valueOf(year) + "-" + month + "-" + i;
      }
      list.add(aDate);
    }
    return list;
  }

  /**
   * @Description 获取Excel表格事件转换字符串
   * <AUTHOR>
   * @Date 2019/12/9 13:18
   * @Return String
   * @Param ditNumber 日期数字
   * @Throws Exception
   */
  public static String getExcelTime(String ditNumber) {
    //如果不是数字
    if (!ObjectUtils.isNumeric(ditNumber)) {
      return null;
    }
    //如果是数字 小于0则 返回
    BigDecimal bd = new BigDecimal(ditNumber);
    int days = bd.intValue();//天数
    int mills = (int) Math.round(bd.subtract(new BigDecimal(days)).doubleValue() * 24 * 3600);

    //获取时间
    Calendar c = Calendar.getInstance();
    c.set(1900, 0, 1);
    c.add(Calendar.DATE, days - 2);
    int hour = mills / 3600;
    int minute = (mills - hour * 3600) / 60;
    int second = mills - hour * 3600 - minute * 60;
    c.set(Calendar.HOUR_OF_DAY, hour);
    c.set(Calendar.MINUTE, minute);
    c.set(Calendar.SECOND, second);

    Date d = c.getTime();//Date
    Timestamp t = Timestamp.valueOf(sdfTime.format(c.getTime()));//Timestamp
    try {
      //时间戳区间判断
      if (minTime <= d.getTime() && d.getTime() <= maxTime) {
        return sdfTime.format(c.getTime());
      } else {
        return "outOfRange";
      }
    } catch (Exception e) {
      System.out.println("传入日期错误" + c.getTime());
    }
    return "Error";
  }

  /**
   * 按照参数format的格式，日期转字符串
   *
   * @param date
   * @param format
   * @return
   */
  public static String date2Str(Date date, String format) {
    if (date != null) {
      SimpleDateFormat sdf = new SimpleDateFormat(format);
      return sdf.format(date);
    } else {
      return "";
    }
  }

  /**
   * @Description 获取当前年份数字
   * <AUTHOR>
   * @Date 2019/11/30 11:29
   * @Return
   * @Param
   * @Throws Exception
   */
  public static int getYearNumber() {
    Calendar c = Calendar.getInstance();
    int year = c.get(Calendar.YEAR);//获取年份，需要与数据库对比
    return year;
  }


  public static int getYearNumberNext() {
    Calendar c = Calendar.getInstance();
    c.add(Calendar.MONTH, 1);
    int year = c.get(Calendar.YEAR);//获取年份，需要与数据库对比
    return year;
  }

  /**
   * @Description 获取当你前月份数字
   * <AUTHOR>
   * @Date 2019/11/30 11:29
   * @Return
   * @Param
   * @Throws Exception
   */
  public static int getMonthNumber() {
    Calendar c = Calendar.getInstance();
    int month = c.get(Calendar.MONTH) + 1;
    if (month > 12) {
      month = 1;
    }
    return month;
  }

  public static int getMonthNumberNext() {
    Calendar c = Calendar.getInstance();
    c.add(Calendar.MONTH, 1);
    int month = c.get(Calendar.MONTH) + 1;
    if (month > 12) {
      month = 1;
    }
    return month;
  }

  /**
   * @Description 获取当前日期数字
   * <AUTHOR>
   * @Date 2019/11/30 11:29
   * @Return
   * @Param
   * @Throws Exception
   */
  public static int getDateNumber() {
    Calendar c = Calendar.getInstance();
    int date = c.get(Calendar.DAY_OF_MONTH);
    return date;
  }

  /**
   * @Description 获取两个日期之间的天数(新的减旧的)
   * <AUTHOR>
   * @Date 2019/12/1 13:06
   * @Return int
   * @Param String newDate 新日期（大的） String oldDate 旧日期
   * @Throws Exception
   */
  public static int getDaysForDate(String newDate, String oldDate) throws Exception {
    Date a1 = new SimpleDateFormat("yyyy-MM-dd").parse(oldDate);
    Date b1 = new SimpleDateFormat("yyyy-MM-dd").parse(newDate);
    long day = (b1.getTime() - a1.getTime()) / (24 * 60 * 60 * 1000);
    return (int) day;
  }

  /**
   * @Description 获取两个时间段的小时数(新的减旧的, 第一个参数减第二个参数)
   * <AUTHOR>
   * @Date 2020/2/5 16:30
   * @Return
   * @Param
   * @Throws Exception
   */
  public static double getHoursForDate(String newDate, String oldDate) throws Exception {
    Date a1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(oldDate);
    Date b1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(newDate);
    long time = b1.getTime() - a1.getTime();
    double hours = (time * 1.0) / (60 * 60 * 1000);
    return hours;
  }


  /**
   * @Description 获取两个时间类有多少个周末(两个周末就是4天)
   * <AUTHOR>
   * @Date 2020/2/5 17:32
   * @Return
   * @Param
   * @Throws Exception
   */
  public static int computeHolidays(String newDate, String oldDate) throws ParseException {
    Date t1 = new SimpleDateFormat("yyyy-MM-dd").parse(oldDate);
    Date t2 = new SimpleDateFormat("yyyy-MM-dd").parse(newDate);
    //初始化第一个日期
    Calendar cal1 = Calendar.getInstance();
    //初始化第二个日期，这里的天数可以随便的设置
    Calendar cal2 = Calendar.getInstance();
    // 设置传入的时间格式
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    // 指定一个日期
    Date date1 = dateFormat.parse(dateFormat.format(t1));
    Date date2 = dateFormat.parse(dateFormat.format(t2));
    // 对 calendar 设置为 date 所定的日期
    cal1.setTime(date1);
    cal2.setTime(date2);
    int holidays = 0;
    //确定一个 大日期
    if (cal1.compareTo(cal2) > 0) {
      Calendar temp = cal1;
      cal1 = cal2;
      cal2 = temp;
      temp = null;
    }
    while (cal1.compareTo(cal2) <= 0) {
      if (cal1.get(Calendar.DAY_OF_WEEK) == 1 || cal1.get(Calendar.DAY_OF_WEEK) == 7) {
        holidays++;
      }
      cal1.add(Calendar.DAY_OF_YEAR, 1);
    }
    return holidays;
  }


  public static List<String> getFinanceDate(int year, int month) {
    List<String> dataList = new ArrayList<>();
    String months = "";
    if (month < 10) {
      months = "0" + month;
    } else {
      months = month + "";
    }
    String startDate = year + "-" + months + "-16";
    if (month > 11) {
      year++;
      months = "01";
    } else {
      month++;
      if (month < 10) {
        months = "0" + month;
      } else {
        months = "" + month;
      }
    }
    String endDate = year + "-" + months + "-15";
    dataList.add(startDate);
    dataList.add(endDate);
    return dataList;
  }


  public static List<String> getFinanceDate(int year, int month, int day) {
    LocalDate inputDate = LocalDate.of(year, month, day);
    LocalDate startDate, endDate;

    if (day < 16) {
      startDate = inputDate.minusMonths(1).withDayOfMonth(16);
      endDate = inputDate.withDayOfMonth(16);
    } else {
      startDate = inputDate.withDayOfMonth(16);
      endDate = inputDate.plusMonths(1).withDayOfMonth(16);
    }

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    List<String> result = new ArrayList<>();
    result.add(startDate.format(formatter));
    result.add(endDate.format(formatter));

    return result;
  }


  /**
   * @Description 根据月份查询英文缩写
   * <AUTHOR>
   * @Date 2019/12/18 10:04
   * @Return String
   * @Param month 月份
   * @Throws Exceptio
   */
  public static String getMonthName(int month) {
    String monthArray[] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct",
        "Nov", "Dec"};
    return monthArray[month - 1];
  }


  public static Date getFirstFinancialDayOfCurMonth() {
    //处理时区差异
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.MONTH, 0);
    calendar.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
    calendar.add(Calendar.DAY_OF_MONTH, 15);

    calendar.set(Calendar.HOUR_OF_DAY, 8);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);

    return calendar.getTime();
  }

  public static Date getFirstFinancialDayOfCurMonthNext() {
    //处理时区差异
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.MONTH, 1);
    calendar.add(Calendar.MONTH, 0);
    calendar.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
    calendar.add(Calendar.DAY_OF_MONTH, 15);

    calendar.set(Calendar.HOUR_OF_DAY, 8);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);

    return calendar.getTime();
  }

  public static String convertKayangDate(String originalDateString) {
    String result = null;
    try {
      // 嘉扬过来日期格式为：M/d/yyyy h:mm:ss a（9/30/2022 12:00:00 AM）
      // 需要同时兼容导入时的正常格式：yyyy-MM-dd HH:mm:ss（2022-09-30 00:00:00）
      if (originalDateString.contains("AM")) {
        // 使用原始日期时间字符串的格式创建一个SimpleDateFormat，并设置地区为US
        SimpleDateFormat originalFormat = new SimpleDateFormat("M/d/yyyy h:mm:ss a", Locale.US);

        // 将原始日期时间字符串解析为Date
        Date date = originalFormat.parse(originalDateString);

        // 创建一个新的SimpleDateFormat，用于输出我们想要的日期格式
        SimpleDateFormat targetFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 将Date格式化为我们想要的日期字符串
        result = targetFormat.format(date);

      } else {
        result = originalDateString;
      }
    } catch (Exception e) {
      e.printStackTrace();
    }
    return result.substring(0, 10);
  }

  public static String addDayToDate(String date, int days) {
    LocalDate date1 = LocalDate.parse(date);
    LocalDate date2 = date1.plusDays(days);
    return date2.toString();
  }

  public static long getDaysDifference(String startTime, String endTime) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime startDate = LocalDateTime.parse(startTime, formatter);
    LocalDateTime endDate = LocalDateTime.parse(endTime, formatter);

    return ChronoUnit.DAYS.between(startDate, endDate) + 1;
  }

  /**
   * @param startTime
   * @param endTime
   * @return
   * @Description 开始日期是否在结束日期之后
   */
  public static boolean timeCompare(String startTime, String endTime) {
    LocalTime time1 = LocalTime.parse(startTime);
    LocalTime time2 = LocalTime.parse(endTime);

    if (time1.isAfter(time2)) {
      return true;
    } else {
      return false;
    }

  }

  public static String getDateStart(String day) {
    LocalDate date = LocalDate.parse(day);
    LocalDateTime startOfDay = date.atStartOfDay();

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    return startOfDay.format(formatter);

  }

  public static String getDateEnd(String day) {
    LocalDate date = LocalDate.parse(day);
    LocalDateTime endOfDay = LocalDateTime.of(date, LocalTime.of(23, 59, 59));

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    return endOfDay.format(formatter);

  }

  public static String getWorkTypeDateStart(String day) {
    LocalDate date = LocalDate.parse(day);
    LocalDateTime endOfDay = LocalDateTime.of(date, LocalTime.of(6, 0, 0));

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    return endOfDay.format(formatter);

  }

  public static String getWorkTypeDateEnd(String day) {
    LocalDate date = LocalDate.parse(day);
    date = date.plusDays(1);
    LocalDateTime endOfDay = LocalDateTime.of(date, LocalTime.of(8, 0, 0));

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    return endOfDay.format(formatter);

  }

  public static String getYesterdayDateFormatted() {
    // Get today's date
    LocalDate today = LocalDate.now();

    // Calculate yesterday's date
    LocalDate yesterday = today.minusDays(1);

    // Format the date in 'yyyy-MM-dd' format
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    return yesterday.format(formatter);
  }


  public static void main(String[] args) throws Exception {
//        Long time1 = new Date().getTime();
//        System.out.println("开始时间："+time1);
//
//        List<EmployeeEntity> paraList = new ArrayList<>();
//        Thread.sleep(30000);
//        EmployeeEntity employeeEntity;
//        for(int i = 0;i<10000000;i++){
//            employeeEntity = new EmployeeEntity();
//            employeeEntity.setEmpId("NB000608");
//            paraList.add(employeeEntity);
//        }
//        Thread.sleep(30000);

//    System.out.println(getFirstFinancialDayOfCurMonth());
//    System.out.println(getFirstFinancialDayOfCurMonthNext());
//    System.out.println(getYearNumberNext());
    System.out.println(getMonthNumberNext());
//
//    List<String> data = getWorkDayOfMonth(2024,1);
//    for (String s : data) {
//      System.out.println(s);
//    }

//    System.out.println(getYesterdayDateFormatted());
//
//    System.out.println(convertKayangDate("1/1/2023 12:00:00 AM"));
//        Long time2 = new Date().getTime();
//        System.out.println("结束时间："+time2);
//        System.out.println("相差时间："+(time2-time1));

    // 测试新的2个财务月方法
    System.out.println("=== 测试2个财务月日期生成 ===");
    List<String> twoMonthDates = getWorkDayOfTwoMonths(2025, 9);
    System.out.println("2024年1月开始的2个财务月日期数量: " + twoMonthDates.size());
    System.out.println("开始日期: " + twoMonthDates.get(0));
    System.out.println("结束日期: " + twoMonthDates.get(twoMonthDates.size() - 1));

//    System.out.println(addDayToDate("2023-01-01", 1));
//    List<String> data = getWorkDayOfMonth(2023,6);
//    for (String s : data) {
//      System.out.println(s);
//    }
//    List<String> data = getFinanceDate(2025,6,25);
//    System.out.println(data);
//    for (String s : data) {
//      System.out.println(s);
//    }
//    System.out.println(getFinanceDateRange());
//    System.out.println(getDaysDifference("2023-06-01 08:00:00", "2023-06-01 23:00:00"));

//    System.out.println("开始时间: " + getDateStart("2023-07-07"));
//    System.out.println("结束时间: " + getDateEnd("2023-07-07"));
//    System.out.println(getAfterDayDate("2024-12-24",-6));

//    String empIds = "NB000000,";
//    String openIds = "'',";
//
//    String[] selectedEmpIds = empIds.split(",");
//    String[] selectedOpenIds = openIds.split(",");
//
//    System.out.println(selectedOpenIds[0].equals("''"));
//
//    System.out.println(selectedEmpIds[0]);
//    System.out.println(selectedOpenIds[0]);

//    DateUtil.getDayByMonth(2024, 1).forEach(System.out::println);

//    System.out.println(DateUtil.checkDayOfWeekend("2024-01-07"));

  }

}
